import React, { useState, useEffect } from "react";
import IconoRegresar from "../../../assets/SVG/IconoRegresar";
import { RoutesPrivate } from "../../../Routes/ProtectedRoute";
import { useNavigate, useLocation } from "react-router-dom";
import { formatearFechaDDMMYYYY, obtenerNombreMesDesdeFecha, obtenerAñoDesdeFecha } from "../../../utils/dateUtils";
import IconoSimulaciones from "../../../assets/SVG/IconoSimulaciones";
import IconoFlechaSimular from "../../../assets/SVG/IconoFlechaSimular";
import IconoInformacion from "../../../assets/SVG/IconoInformacion";
import IconoCalendar from "../../../assets/SVG/IconoCalendar";
import IconoVer from "../../../assets/SVG/IconoVer";
import { BarChart, Bar, LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import FinancieraService from "../../../Services/FinancieraService";
import Cookies from "js-cookie";
import { encrypt, decrypt } from "../../../Services/TokenService";

const DetalleEF = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // Estados para los parámetros recibidos
  const [tipoPeriodo, setTipoPeriodo] = useState(null);
  const [fechaFin, setFechaFin] = useState(null);
  const [nombrePeriodo, setNombrePeriodo] = useState("Abril");
  const [periodoData, setPeriodoData] = useState(null);

  // Estado para el título del periodo
  const [tituloPeriodo, setTituloPeriodo] = useState("Periodo contable Abril-2025");

  // Estados para el selector de empresas
  const [empresas, setEmpresas] = useState([]);
  const [empresaSeleccionada, setEmpresaSeleccionada] = useState(null);
  const [cargandoEmpresas, setCargandoEmpresas] = useState(false);
  const [errorEmpresas, setErrorEmpresas] = useState(null);

  // Estados para el selector de periodos
  const [periodos, setPeriodos] = useState([]);
  const [periodoSeleccionado, setPeriodoSeleccionado] = useState(null);
  const [cargandoPeriodos, setCargandoPeriodos] = useState(false);
  const [errorPeriodos, setErrorPeriodos] = useState(null);

  // Estado para la fecha de subida
  const [fechaSubida, setFechaSubida] = useState(null);

  // Estados para los ratios financieros
  const [ratiosFinancieros, setRatiosFinancieros] = useState([]);
  const [cargandoRatios, setCargandoRatios] = useState(false);
  const [errorRatios, setErrorRatios] = useState(null);

  // Estados para los datos financieros transformados
  const [datosFinancierosTransformados, setDatosFinancierosTransformados] = useState([]);
  const [cargandoDatosFinancieros, setCargandoDatosFinancieros] = useState(false);
  const [errorDatosFinancieros, setErrorDatosFinancieros] = useState(null);

  // Estados para los detalles de cuentas de estados financieros
  const [detallesCuentas, setDetallesCuentas] = useState([]);
  const [cargandoDetallesCuentas, setCargandoDetallesCuentas] = useState(false);
  const [errorDetallesCuentas, setErrorDetallesCuentas] = useState(null);

  // Estados para la moneda
  const [moneda, setMoneda] = useState("USD");
  const [simboloMoneda, setSimboloMoneda] = useState("$");

  // Función para obtener la moneda desde las cookies
  const obtenerMonedaDesdeCookies = () => {
    try {
      const monedaCookie = Cookies.get("monedaFinanciera");
      const simboloCookie = Cookies.get("simbMonedaFinanciera");

      if (monedaCookie && simboloCookie) {
        const monedaDecrypted = decrypt(monedaCookie);
        let simboloDecrypted = decrypt(simboloCookie);

        if (monedaDecrypted && simboloDecrypted) {
          // Regla especial: si el símbolo es "S/." quitar el punto final
          if (simboloDecrypted === "S/.") {
            simboloDecrypted = "S/";
          }

          setMoneda(monedaDecrypted);
          setSimboloMoneda(simboloDecrypted);
          return { moneda: monedaDecrypted, simbolo: simboloDecrypted };
        }
      }
    } catch (error) {
      console.error("Error al obtener moneda desde cookies:", error);
    }

    // Valores por defecto si no se pueden obtener desde cookies
    return { moneda: "USD", simbolo: "$" };
  };

  // Función para formatear números con separadores de miles y símbolo de moneda
  const formatearNumero = (numero, conSeparador = true, conMoneda = false) => {
    if (numero === null || numero === undefined) return conMoneda ? `${simboloMoneda}0` : "0";

    // Redondear a 2 decimales
    const numeroRedondeado = Math.round(numero * 100) / 100;

    let numeroFormateado;
    if (conSeparador) {
      // Formatear con separadores de miles usando coma como separador de miles
      numeroFormateado = numeroRedondeado.toLocaleString('es-ES').replace(/\./g, ',');
    } else {
      // Sin separadores para los datos de gráficos
      numeroFormateado = numeroRedondeado.toString();
    }

    // Agregar símbolo de moneda si se solicita
    return conMoneda ? `${simboloMoneda}${numeroFormateado}` : numeroFormateado;
  };

  // Función para transformar los datos del backend
  const transformarDatosFinancieros = (datosBackend) => {
    if (!datosBackend || !datosBackend.data || !Array.isArray(datosBackend.data)) {
      return [];
    }

    return datosBackend.data.map(periodo => {
      // Consolidar todos los agrupadores de todos los estados financieros en un solo array
      const agrupadores = [];

      if (periodo.estados_financieros && Array.isArray(periodo.estados_financieros)) {
        periodo.estados_financieros.forEach(estadoFinanciero => {
          if (estadoFinanciero.agrupadores && Array.isArray(estadoFinanciero.agrupadores)) {
            estadoFinanciero.agrupadores.forEach(agrupador => {
              agrupadores.push({
                ...agrupador,
                tipo_estado_financiero: estadoFinanciero.tipo_estado_financiero,
                id_estado_financiero: estadoFinanciero.id_estado_financiero
              });
            });
          }
        });
      }

      return {
        fecha_fin: periodo.fecha_fin,
        agrupadores: agrupadores
      };
    });
  };

  // Función para obtener los valores de Activos, Pasivos y Patrimonio del periodo más reciente
  const obtenerDatosFinancierosActuales = () => {
    if (!datosFinancierosTransformados || datosFinancierosTransformados.length === 0) {
      return {
        activosTotales: null,
        pasivosTotales: null,
        patrimonio: null,
        variacionActivos: null,
        variacionPasivos: null,
        variacionPatrimonio: null
      };
    }

    const periodoActual = datosFinancierosTransformados[0]; // El primer periodo es el más reciente
    const agrupadores = periodoActual.agrupadores || [];

    // Buscar activos totales (puede estar como "activos" o "activo total")
    const activosTotales = agrupadores.find(agrupador =>
      agrupador.nombre_agrupador?.toLowerCase().includes('activo') &&
      agrupador.tipo_estado_financiero === 1 && // Balance General
      !agrupador.nombre_subagrupador // Solo el total, no los subgrupos
    );

    // Buscar pasivos totales
    const pasivosTotales = agrupadores.find(agrupador =>
      agrupador.nombre_agrupador?.toLowerCase().includes('pasivo') &&
      agrupador.tipo_estado_financiero === 1 && // Balance General
      !agrupador.nombre_subagrupador // Solo el total, no los subgrupos
    );

    // Buscar patrimonio
    const patrimonio = agrupadores.find(agrupador =>
      agrupador.nombre_agrupador?.toLowerCase().includes('patrimonio') &&
      agrupador.tipo_estado_financiero === 1 // Balance General
    );

    return {
      activosTotales: activosTotales?.valor || null,
      pasivosTotales: pasivosTotales?.valor || null,
      patrimonio: patrimonio?.valor || null,
      variacionActivos: activosTotales?.variacion || null,
      variacionPasivos: pasivosTotales?.variacion || null,
      variacionPatrimonio: patrimonio?.variacion || null
    };
  };

  // Función para calcular la composición financiera
  const calcularComposicionFinanciera = () => {
    if (!datosFinancierosTransformados || datosFinancierosTransformados.length === 0) {
      return {
        activoCorriente: 0,
        activoNoCorriente: 0,
        pasivoCorriente: 0,
        pasivoNoCorriente: 0,
        patrimonio: 0,
        porcentajeActivoCorriente: 0,
        porcentajeActivoNoCorriente: 0,
        porcentajePasivoCorriente: 0,
        porcentajePasivoNoCorriente: 0,
        porcentajePatrimonio: 0
      };
    }

    const periodoActual = datosFinancierosTransformados[0];
    const agrupadores = periodoActual.agrupadores || [];

    // Buscar activos corrientes y no corrientes
    const activoCorriente = agrupadores.find(agrupador =>
      agrupador.nombre_agrupador?.toLowerCase().includes('activo') &&
      agrupador.nombre_subagrupador?.toLowerCase().includes('corriente') &&
      agrupador.tipo_estado_financiero === 1
    );

    const activoNoCorriente = agrupadores.find(agrupador =>
      agrupador.nombre_agrupador?.toLowerCase().includes('activo') &&
      agrupador.nombre_subagrupador?.toLowerCase().includes('no corriente') &&
      agrupador.tipo_estado_financiero === 1
    );

    // Buscar pasivos corrientes y no corrientes
    const pasivoCorriente = agrupadores.find(agrupador =>
      agrupador.nombre_agrupador?.toLowerCase().includes('pasivo') &&
      agrupador.nombre_subagrupador?.toLowerCase().includes('corriente') &&
      agrupador.tipo_estado_financiero === 1
    );

    const pasivoNoCorriente = agrupadores.find(agrupador =>
      agrupador.nombre_agrupador?.toLowerCase().includes('pasivo') &&
      agrupador.nombre_subagrupador?.toLowerCase().includes('no corriente') &&
      agrupador.tipo_estado_financiero === 1
    );

    // Buscar patrimonio
    const patrimonio = agrupadores.find(agrupador =>
      agrupador.nombre_agrupador?.toLowerCase().includes('patrimonio') &&
      agrupador.tipo_estado_financiero === 1
    );

    const valorActivoCorriente = Math.abs(activoCorriente?.valor || 0);
    const valorActivoNoCorriente = Math.abs(activoNoCorriente?.valor || 0);
    const valorPasivoCorriente = Math.abs(pasivoCorriente?.valor || 0);
    const valorPasivoNoCorriente = Math.abs(pasivoNoCorriente?.valor || 0);
    const valorPatrimonio = Math.abs(patrimonio?.valor || 0);

    const totalActivos = valorActivoCorriente + valorActivoNoCorriente;
    const totalPasivoPatrimonio = valorPasivoCorriente + valorPasivoNoCorriente + valorPatrimonio;

    return {
      activoCorriente: valorActivoCorriente,
      activoNoCorriente: valorActivoNoCorriente,
      pasivoCorriente: valorPasivoCorriente,
      pasivoNoCorriente: valorPasivoNoCorriente,
      patrimonio: valorPatrimonio,
      porcentajeActivoCorriente: totalActivos > 0 ? (valorActivoCorriente / totalActivos) * 100 : 0,
      porcentajeActivoNoCorriente: totalActivos > 0 ? (valorActivoNoCorriente / totalActivos) * 100 : 0,
      porcentajePasivoCorriente: totalPasivoPatrimonio > 0 ? (valorPasivoCorriente / totalPasivoPatrimonio) * 100 : 0,
      porcentajePasivoNoCorriente: totalPasivoPatrimonio > 0 ? (valorPasivoNoCorriente / totalPasivoPatrimonio) * 100 : 0,
      porcentajePatrimonio: totalPasivoPatrimonio > 0 ? (valorPatrimonio / totalPasivoPatrimonio) * 100 : 0
    };
  };

  // useEffect para procesar los parámetros recibidos desde Inicio.jsx
  useEffect(() => {
    if (location.state) {
      const { tipoPeriodo: tipoPeriodoRecibido, fechaFin: fechaFinRecibida, nombrePeriodo: nombrePeriodoRecibido, periodoData: periodoDataRecibido } = location.state;

      console.log("Parámetros recibidos en DetalleEF:", location.state);
      console.log("Tipo de periodo:", tipoPeriodoRecibido, "(1: anual, 2: trimestral, 3: mensual)");
      console.log("Fecha fin (DD/MM/YYYY):", fechaFinRecibida);
      console.log("Nombre del periodo:", nombrePeriodoRecibido);
      console.log("Datos del periodo:", periodoDataRecibido);

      // Actualizar estados con los parámetros recibidos
      setTipoPeriodo(tipoPeriodoRecibido);
      setFechaFin(fechaFinRecibida);
      setNombrePeriodo(nombrePeriodoRecibido);
      setPeriodoData(periodoDataRecibido);

      // Generar título del periodo basado en el tipo de periodo
      let nuevoTitulo = "Periodo contable";

      if (tipoPeriodoRecibido && fechaFinRecibida && nombrePeriodoRecibido) {
        // Obtener el año de la fecha fin
        const año = fechaFinRecibida.split('/')[2]; // Formato DD/MM/YYYY

        switch (tipoPeriodoRecibido) {
          case 1: // Anual
            nuevoTitulo = `Periodo contable Anual ${año}`;
            break;
          case 2: // Trimestral
            nuevoTitulo = `Periodo contable Trimestral ${nombrePeriodoRecibido}-${año}`;
            break;
          case 3: // Mensual
            nuevoTitulo = `Periodo contable ${nombrePeriodoRecibido}-${año}`;
            break;
          default:
            nuevoTitulo = `Periodo contable ${nombrePeriodoRecibido}-${año}`;
        }
      }

      setTituloPeriodo(nuevoTitulo);

      console.log("Título del periodo actualizado:", nuevoTitulo);
      console.log("Tipo de periodo:", tipoPeriodoRecibido, "(1: anual, 2: trimestral, 3: mensual)");
      console.log("Fecha fin (DD/MM/YYYY):", fechaFinRecibida);
    }
  }, [location.state]);

  // Efecto para cargar las empresas al montar el componente
  useEffect(() => {
    const cargarEmpresas = async () => {
      try {
        setCargandoEmpresas(true);
        setErrorEmpresas(null);
        const data = await FinancieraService.getEmpresas();
        setEmpresas(data);

        // Obtener el ID de empresa desde las cookies
        const empresaIdFromCookie = FinancieraService.getCompanyId();

        if (data && data.length > 0) {
          if (empresaIdFromCookie) {
            // Buscar la empresa que coincida con el ID de la cookie
            const empresaEncontrada = data.find(emp => emp.int_idEmpresa === empresaIdFromCookie);
            if (empresaEncontrada) {
              setEmpresaSeleccionada(empresaEncontrada);
            } else {
              // Si no se encuentra la empresa de la cookie, seleccionar la primera
              setEmpresaSeleccionada(data[0]);
            }
          } else {
            // Si no hay ID en cookie, seleccionar la primera empresa
            setEmpresaSeleccionada(data[0]);
          }
        }
      } catch (error) {
        console.error("Error al cargar empresas:", error);
        setErrorEmpresas(error.message || "Error al cargar las empresas");
      } finally {
        setCargandoEmpresas(false);
      }
    };

    cargarEmpresas();
  }, []);

  // Efecto para cargar periodos cuando tipoPeriodo esté disponible
  useEffect(() => {
    if (tipoPeriodo && empresaSeleccionada) {
      cargarPeriodos(tipoPeriodo);
    }
  }, [tipoPeriodo, empresaSeleccionada]);

  // Efecto para cargar ratios financieros cuando los parámetros estén disponibles
  useEffect(() => {
    if (tipoPeriodo && fechaFin && empresaSeleccionada) {
      cargarRatiosFinancieros(tipoPeriodo, fechaFin);
    }
  }, [tipoPeriodo, fechaFin, empresaSeleccionada]);

  // Efecto para cargar datos financieros transformados cuando los parámetros estén disponibles
  useEffect(() => {
    if (tipoPeriodo && fechaFin && empresaSeleccionada) {
      cargarDatosFinancierosTransformados(tipoPeriodo, fechaFin);
    }
  }, [tipoPeriodo, fechaFin, empresaSeleccionada]);

  // Efecto para cargar detalles de cuentas cuando los parámetros estén disponibles
  useEffect(() => {
    if (tipoPeriodo && fechaFin && empresaSeleccionada) {
      cargarDetallesCuentas(tipoPeriodo, fechaFin);
    }
  }, [tipoPeriodo, fechaFin, empresaSeleccionada]);

  // Efecto para cargar la información de moneda desde cookies
  useEffect(() => {
    obtenerMonedaDesdeCookies();
  }, [empresaSeleccionada]); // Se ejecuta cuando cambia la empresa seleccionada

  // Manejar cambio de empresa
  const handleEmpresaChange = (empresa) => {
    setEmpresaSeleccionada(empresa);

    // Guardar el ID de la empresa en las cookies usando encrypt
    if (empresa && empresa.int_idEmpresa) {
      Cookies.set("busFinanciera", encrypt(empresa.int_idEmpresa.toString()));
      // Guardar en cookies moneda y simbolo
      Cookies.set("monedaFinanciera", encrypt(empresa.str_Moneda));
      Cookies.set("simbMonedaFinanciera", encrypt(empresa.str_SimboloMoneda));
      console.log("Empresa seleccionada guardado en cookies:", empresa);
    }

    // Cargar periodos para la nueva empresa
    if (tipoPeriodo) {
      cargarPeriodos(tipoPeriodo);
    }

    // Cargar ratios financieros para la nueva empresa si tenemos los parámetros necesarios
    if (tipoPeriodo && fechaFin) {
      cargarRatiosFinancieros(tipoPeriodo, fechaFin);
      cargarDatosFinancierosTransformados(tipoPeriodo, fechaFin);
      cargarDetallesCuentas(tipoPeriodo, fechaFin);
    }
  };

  // Función para cargar los periodos
  const cargarPeriodos = async (tipoPeriodoParam) => {
    try {
      setCargandoPeriodos(true);
      setErrorPeriodos(null);
      const data = await FinancieraService.getEstadosFinancierosPeriodos(tipoPeriodoParam);
      setPeriodos(data);

      // Buscar el periodo que coincida con fechaFinRecibida
      if (data && data.length > 0 && fechaFin) {
        const periodoEncontrado = buscarPeriodoPorFechaFin(data, fechaFin);
        if (periodoEncontrado) {
          setPeriodoSeleccionado(periodoEncontrado);
          // Actualizar la fecha de subida para el periodo encontrado
          const fechaSubidaObtenida = obtenerFechaSubida(periodoEncontrado);
          setFechaSubida(fechaSubidaObtenida);
        } else {
          // Si no se encuentra, seleccionar el primer periodo
          setPeriodoSeleccionado(data[0]);
          // Actualizar la fecha de subida para el primer periodo
          const fechaSubidaObtenida = obtenerFechaSubida(data[0]);
          setFechaSubida(fechaSubidaObtenida);
        }
      } else if (data && data.length > 0) {
        // Si no hay fechaFin, seleccionar el primer periodo
        setPeriodoSeleccionado(data[0]);
        // Actualizar la fecha de subida para el primer periodo
        const fechaSubidaObtenida = obtenerFechaSubida(data[0]);
        setFechaSubida(fechaSubidaObtenida);
      }
    } catch (error) {
      console.error("Error al cargar periodos:", error);
      setErrorPeriodos(error.message || "Error al cargar los periodos");
    } finally {
      setCargandoPeriodos(false);
    }
  };

  // Función para buscar un periodo por fecha fin
  const buscarPeriodoPorFechaFin = (periodosData, fechaFinBuscada) => {
    console.log("Buscando periodo por fecha fin:", fechaFinBuscada);
    console.log("Periodos disponibles:", periodosData);
    for (const periodo of periodosData) {
      for (const estadoFinanciero of periodo.estados_financieros) {
        const fechaFinFormateada = formatearFechaDDMMYYYY(estadoFinanciero.dt_fechaFinPeriodo);
        console.log("Fecha fin formateada:", fechaFinFormateada);
        if (fechaFinFormateada === fechaFinBuscada) {
          console.log("Periodo encontrado:", periodo);
          return periodo;
        }
      }
    }
    return null;
  };

  // Función para formatear el nombre del periodo según el tipo
  const formatearNombrePeriodo = (periodo, tipoPeriodoParam) => {
    if (!periodo || !periodo.periodo_contable) return "Periodo desconocido";

    const periodoContable = periodo.periodo_contable;

    if (tipoPeriodoParam === 1) {
      // Para tipo anual, mostrar solo el año
      // Extraer el año del string "February 2025" -> "2025"
      const partes = periodoContable.split(' ');
      if (partes.length >= 2) {
        return partes[partes.length - 1]; // Último elemento es el año
      }
      return periodoContable;
    } else if (tipoPeriodoParam === 3) {
      // Para tipo mensual, mostrar mes y año: "Enero - 2025"
      const partes = periodoContable.split(' ');
      if (partes.length >= 2) {
        const mesIngles = partes[0];
        const año = partes[partes.length - 1];

        // Convertir mes de inglés a español
        const mesesTraduccion = {
          'January': 'Enero',
          'February': 'Febrero',
          'March': 'Marzo',
          'April': 'Abril',
          'May': 'Mayo',
          'June': 'Junio',
          'July': 'Julio',
          'August': 'Agosto',
          'September': 'Septiembre',
          'October': 'Octubre',
          'November': 'Noviembre',
          'December': 'Diciembre'
        };

        const mesEspañol = mesesTraduccion[mesIngles] || mesIngles;
        return `${mesEspañol} - ${año}`;
      }
      return periodoContable;
    }

    // Para otros tipos, devolver tal como viene
    return periodoContable;
  };

  // Función para obtener la fecha de subida del estado financiero tipo 1
  const obtenerFechaSubida = (periodo) => {
    if (!periodo || !periodo.estados_financieros || !Array.isArray(periodo.estados_financieros)) {
      return null;
    }

    // Buscar el estado financiero tipo 1 (Balance General)
    const estadoFinancieroTipo1 = periodo.estados_financieros.find(ef => ef.int_tipoEstadoFinanciero === 1);

    if (estadoFinancieroTipo1 && estadoFinancieroTipo1.dt_fechaRegistro) {
      // Formatear la fecha usando dateUtils
      return formatearFechaDDMMYYYY(estadoFinancieroTipo1.dt_fechaRegistro);
    }

    return null;
  };

  // Manejar cambio de periodo
  const handlePeriodoChange = (periodo) => {
    setPeriodoSeleccionado(periodo);
    console.log("Periodo seleccionado:", periodo);

    // Actualizar el título del periodo
    if (periodo && tipoPeriodo) {
      const nombrePeriodoFormateado = formatearNombrePeriodo(periodo, tipoPeriodo);
      let nuevoTitulo = "Periodo contable";

      if (periodo.estados_financieros && periodo.estados_financieros.length > 0) {
        // Obtener el año de la fecha fin del primer estado financiero
        const fechaFinPeriodo = formatearFechaDDMMYYYY(periodo.estados_financieros[0].dt_fechaFinPeriodo);
        const año = fechaFinPeriodo.split('/')[2]; // Formato DD/MM/YYYY

        switch (tipoPeriodo) {
          case 1: // Anual
            nuevoTitulo = `Periodo contable Anual ${año}`;
            break;
          case 2: // Trimestral
            nuevoTitulo = `Periodo contable Trimestral ${nombrePeriodoFormateado}-${año}`;
            break;
          case 3: // Mensual
            nuevoTitulo = `Periodo contable ${nombrePeriodoFormateado}`;
            break;
          default:
            nuevoTitulo = `Periodo contable ${nombrePeriodoFormateado}`;
        }
      }

      setTituloPeriodo(nuevoTitulo);
      console.log("Título del periodo actualizado:", nuevoTitulo);
    }

    // Actualizar la fecha de subida
    const fechaSubidaObtenida = obtenerFechaSubida(periodo);
    setFechaSubida(fechaSubidaObtenida);
    console.log("Fecha de subida actualizada:", fechaSubidaObtenida);

    // Cargar ratios financieros para el nuevo periodo si tenemos los parámetros necesarios
    if (periodo && periodo.estados_financieros && periodo.estados_financieros.length > 0 && tipoPeriodo) {
      // Obtener la fecha fin del primer estado financiero del periodo
      const fechaFinPeriodo = formatearFechaDDMMYYYY(periodo.estados_financieros[0].dt_fechaFinPeriodo);
      console.log("Fecha fin del periodo:", fechaFinPeriodo);

      // Actualizar también el estado fechaFin para que se use en otros lugares
      setFechaFin(fechaFinPeriodo);

      cargarRatiosFinancieros(tipoPeriodo, fechaFinPeriodo);
      cargarDatosFinancierosTransformados(tipoPeriodo, fechaFinPeriodo);
      cargarDetallesCuentas(tipoPeriodo, fechaFinPeriodo);
    }
  };

  // Función para cargar los ratios financieros
  const cargarRatiosFinancieros = async (tipoPeriodoParam, fechaFinParam) => {
    try {
      setCargandoRatios(true);
      setErrorRatios(null);

      // Convertir fecha de DD/MM/YYYY a DD-MM-YYYY para el endpoint
      const fechaFinFormateada = fechaFinParam.replace(/\//g, '-');

      const data = await FinancieraService.getRatiosEFPeriodoEspecifico(tipoPeriodoParam, fechaFinFormateada);
      setRatiosFinancieros(data);
      console.log("Ratios financieros cargados:", data);
    } catch (error) {
      console.error("Error al cargar ratios financieros:", error);
      setErrorRatios(error.message || "Error al cargar los ratios financieros");
      setRatiosFinancieros([]); // Limpiar datos en caso de error
    } finally {
      setCargandoRatios(false);
    }
  };

  // Función para cargar los datos financieros transformados
  const cargarDatosFinancierosTransformados = async (tipoPeriodoParam, fechaFinParam) => {
    try {
      setCargandoDatosFinancieros(true);
      setErrorDatosFinancieros(null);

      const data = await FinancieraService.getTotalAgrupadorPeriodoEspecificoUltimosPeriodos(tipoPeriodoParam, fechaFinParam);
      console.log("Datos financieros del backend:", data);
      if (data) {
        const datosTransformados = transformarDatosFinancieros(data);
        setDatosFinancierosTransformados(datosTransformados);
        console.log("Datos financieros transformados cargados:", datosTransformados);
      } else {
        setDatosFinancierosTransformados([]);
      }
    } catch (error) {
      console.error("Error al cargar datos financieros transformados:", error);
      setErrorDatosFinancieros(error.message || "Error al cargar los datos financieros");
      setDatosFinancierosTransformados([]); // Limpiar datos en caso de error
    } finally {
      setCargandoDatosFinancieros(false);
    }
  };

  // Función para cargar los detalles de cuentas de estados financieros
  const cargarDetallesCuentas = async (tipoPeriodoParam, fechaFinParam) => {
    try {
      setCargandoDetallesCuentas(true);
      setErrorDetallesCuentas(null);

      const data = await FinancieraService.getEFCuentasPorEmpresaPeriodo(tipoPeriodoParam, fechaFinParam);
      console.log("Detalles de cuentas del backend:", data);
      if (data && Array.isArray(data)) {
        setDetallesCuentas(data);
        console.log("Detalles de cuentas cargados:", data);
      } else {
        setDetallesCuentas([]);
      }
    } catch (error) {
      console.error("Error al cargar detalles de cuentas:", error);
      setErrorDetallesCuentas(error.message || "Error al cargar los detalles de cuentas");
      setDetallesCuentas([]); // Limpiar datos en caso de error
    } finally {
      setCargandoDetallesCuentas(false);
    }
  };

  // Función para obtener los detalles de cuentas por tipo de agrupador
  const obtenerCuentasPorAgrupador = (nombreAgrupador) => {
    if (!detallesCuentas || detallesCuentas.length === 0) {
      return [];
    }

    // Buscar el estado financiero tipo 1 (Balance General)
    const estadoFinancieroTipo1 = detallesCuentas.find(ef => ef.tipo_estado_financiero === 1);

    if (!estadoFinancieroTipo1 || !estadoFinancieroTipo1.agrupadores) {
      return [];
    }

    // Buscar el agrupador específico
    const agrupador = estadoFinancieroTipo1.agrupadores.find(agr =>
      agr.nombre.toLowerCase().includes(nombreAgrupador.toLowerCase())
    );

    return agrupador ? agrupador.cuentas || [] : [];
  };

  // Función para obtener los datos del agrupador (incluyendo totales)
  const obtenerDatosAgrupador = (nombreAgrupador) => {
    if (!detallesCuentas || detallesCuentas.length === 0) {
      return null;
    }

    // Buscar el estado financiero tipo 1 (Balance General)
    const estadoFinancieroTipo1 = detallesCuentas.find(ef => ef.tipo_estado_financiero === 1);

    if (!estadoFinancieroTipo1 || !estadoFinancieroTipo1.agrupadores) {
      return null;
    }

    // Buscar el agrupador específico
    const agrupador = estadoFinancieroTipo1.agrupadores.find(agr =>
      agr.nombre.toLowerCase().includes(nombreAgrupador.toLowerCase())
    );

    return agrupador || null;
  };

  // Función para obtener parámetros para simulaciones desde DetalleEF
  const obtenerParametrosSimulacionesDetalleEF = () => {
    // 1. Tipo de periodo actual
    const tipoPeriodoActual = tipoPeriodo;

    // 2. Fecha fin actual
    const fechaFinActual = fechaFin;

    // 3. Buscar ID del estado financiero tipo 1
    let idEstadoFinancieroTipo1 = null;

    // Intentar obtener el ID desde diferentes fuentes
    // Opción 1: Desde periodoData (datos recibidos desde Inicio.jsx)
    if (periodoData && periodoData.estados_financieros && Array.isArray(periodoData.estados_financieros)) {
      const estadoFinancieroTipo1 = periodoData.estados_financieros.find(ef => ef.int_tipoEF === 1);
      if (estadoFinancieroTipo1) {
        idEstadoFinancieroTipo1 = estadoFinancieroTipo1.int_idEstadoFinanciero;
      }
    }

    // Opción 2: Desde periodoSeleccionado (periodo actualmente seleccionado)
    if (!idEstadoFinancieroTipo1 && periodoSeleccionado && periodoSeleccionado.estados_financieros && Array.isArray(periodoSeleccionado.estados_financieros)) {
      const estadoFinancieroTipo1 = periodoSeleccionado.estados_financieros.find(ef => ef.int_tipoEstadoFinanciero === 1);
      if (estadoFinancieroTipo1) {
        idEstadoFinancieroTipo1 = estadoFinancieroTipo1.int_idEstadoFinanciero;
      }
    }

    // Opción 3: Desde detallesCuentas (datos de cuentas cargados)
    if (!idEstadoFinancieroTipo1 && detallesCuentas && Array.isArray(detallesCuentas)) {
      const estadoFinancieroTipo1 = detallesCuentas.find(ef => ef.tipo_estado_financiero === 1);
      if (estadoFinancieroTipo1) {
        idEstadoFinancieroTipo1 = estadoFinancieroTipo1.id_estado_financiero;
      }
    }

    // Validar que tenemos los datos mínimos necesarios
    if (!tipoPeriodoActual || !fechaFinActual) {
      console.warn("No hay datos suficientes para obtener parámetros de simulaciones desde DetalleEF");
      return null;
    }

    return {
      tipoPeriodo: tipoPeriodoActual,
      fechaFinPeriodo: fechaFinActual, // Ya está en formato DD/MM/YYYY
      idEstadoFinanciero: idEstadoFinancieroTipo1
    };
  };

  // Función para navegar a simulaciones con parámetros desde DetalleEF
  const navegarASimulacionesDesdeDetalleEF = () => {
    const parametros = obtenerParametrosSimulacionesDetalleEF();

    if (parametros) {
      console.log("Navegando a Simulaciones desde DetalleEF con parámetros:", parametros);
      navigate(RoutesPrivate.SIMULACIONES, {
        state: parametros
      });
    } else {
      console.log("Navegando a Simulaciones desde DetalleEF sin parámetros");
      navigate(RoutesPrivate.SIMULACIONES);
    }
  };

  // Función para obtener los valores totales de activos, pasivos y patrimonio desde detallesCuentas
  const obtenerTotalesDesdeDetallesCuentas = () => {
    if (!detallesCuentas || detallesCuentas.length === 0) {
      return {
        activosTotales: null,
        pasivosTotales: null,
        patrimonio: null
      };
    }

    // Buscar el estado financiero tipo 1 (Balance General)
    const estadoFinancieroTipo1 = detallesCuentas.find(ef => ef.tipo_estado_financiero === 1);

    if (!estadoFinancieroTipo1 || !estadoFinancieroTipo1.agrupadores) {
      return {
        activosTotales: null,
        pasivosTotales: null,
        patrimonio: null
      };
    }

    // Buscar los agrupadores especiales que vienen del backend
    const activosTotales = estadoFinancieroTipo1.agrupadores.find(agr =>
      agr.nombre.toLowerCase() === "activos totales"
    );

    const pasivosTotales = estadoFinancieroTipo1.agrupadores.find(agr =>
      agr.nombre.toLowerCase() === "pasivos totales"
    );

    // Para patrimonio, buscar el agrupador que contenga "patrimonio"
    const patrimonio = estadoFinancieroTipo1.agrupadores.find(agr =>
      agr.nombre.toLowerCase().includes("patrimonio") && agr.cuentas !== null
    );

    return {
      activosTotales: activosTotales?.valor || null,
      pasivosTotales: pasivosTotales?.valor || null,
      patrimonio: patrimonio?.valor || null
    };
  };

  // Función para formatear el valor de variación (AH - Análisis Horizontal)
  const formatearVariacion = (variacion) => {
    if (variacion === null || variacion === undefined) return null;

    return `${variacion.toFixed(2)}%`;
  };

  // Función para formatear el valor de proporción (AV - Análisis Vertical)
  const formatearProporcion = (proporcion) => {
    if (proporcion === null || proporcion === undefined) return null;

    return `${proporcion.toFixed(2)}%`;
  };

  const [datosMensuales, setDatosMensuales] = useState([
    {
      nombre: "Abril",
      fecha: "31/04/2025",
      indicadores: {
        "Activos totales": {
          valor: "$1,245,600",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Pasivos totales": {
          valor: "$645,300",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#FF6B8A",
        },
        Patrimonio: {
          valor: "$600,30",
          porcentaje: "+ 12.8%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Ratio de Liquidez": {
          valor: "$1,245,600",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Ratio de Endeudamiento": {
          valor: "$645,300",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#FF6B8A",
        },
        "Ratio 3": {
          valor: "$600,30",
          porcentaje: "+ 12.8%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
      },
    },
    {
      nombre: "Marzo",
      fecha: "31/03/2025",
      indicadores: {
        "Activos totales": {
          valor: "$1,245,600",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Pasivos totales": {
          valor: "$645,300",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#FF6B8A",
        },
        Patrimonio: {
          valor: "$600,30",
          porcentaje: "+ 12.8%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Ratio de Liquidez": {
          valor: "$1,245,600",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Ratio de Endeudamiento": {
          valor: "$645,300",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#FF6B8A",
        },
        "Ratio 3": {
          valor: "$600,30",
          porcentaje: "+ 12.8%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
      },
    },
    {
      nombre: "Febrero",
      fecha: "31/02/2025",
      indicadores: {
        "Activos totales": {
          valor: "$1,245,600",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Pasivos totales": {
          valor: "$645,300",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#FF6B8A",
        },
        Patrimonio: {
          valor: "$600,30",
          porcentaje: "+ 12.8%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Ratio de Liquidez": {
          valor: "$1,245,600",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Ratio de Endeudamiento": {
          valor: "$645,300",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#FF6B8A",
        },
        "Ratio 3": {
          valor: "$600,30",
          porcentaje: "+ 12.8%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
      },
    },
    {
      nombre: "Enero",
      fecha: "31/01/2025",
      indicadores: {
        "Activos totales": {
          valor: "$1,545,600",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Pasivos totales": {
          valor: "$685,300",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#FF6B8A",
        },
        Patrimonio: {
          valor: "$680,30",
          porcentaje: "+ 12.8%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Ratio de Liquidez": {
          valor: "$1,985,600",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Ratio de Endeudamiento": {
          valor: "$685,300",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#FF6B8A",
        },
        "Ratio 3": {
          valor: "$680,30",
          porcentaje: "+ 12.8%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
      },
    },
    {
      nombre: "Diciembre",
      fecha: "31/12/2024",
      indicadores: {
        "Activos totales": {
          valor: "$1,545,600",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Pasivos totales": {
          valor: "$685,300",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#FF6B8A",
        },
        Patrimonio: {
          valor: "$680,30",
          porcentaje: "+ 12.8%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Ratio de Liquidez": {
          valor: "$1,985,600",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Ratio de Endeudamiento": {
          valor: "$685,300",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#FF6B8A",
        },
        "Ratio 3": {
          valor: "$680,30",
          porcentaje: "+ 12.8%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
      },
    },
    {
      nombre: "Noviembre",
      fecha: "31/11/2024",
      indicadores: {
        "Activos totales": {
          valor: "$1,545,600",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Pasivos totales": {
          valor: "$685,300",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#FF6B8A",
        },
        Patrimonio: {
          valor: "$680,30",
          porcentaje: "+ 12.8%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Ratio de Liquidez": {
          valor: "$1,985,600",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Ratio de Endeudamiento": {
          valor: "$685,300",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#FF6B8A",
        },
        "Ratio 3": {
          valor: "$680,30",
          porcentaje: "+ 12.8%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
      },
    },
    {
      nombre: "Octubre",
      fecha: "31/10/2024",
      indicadores: {
        "Activos totales": {
          valor: "$1,545,600",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Pasivos totales": {
          valor: "$685,300",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#FF6B8A",
        },
        Patrimonio: {
          valor: "$680,30",
          porcentaje: "+ 12.8%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Ratio de Liquidez": {
          valor: "$1,985,600",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Ratio de Endeudamiento": {
          valor: "$685,300",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#FF6B8A",
        },
        "Ratio 3": {
          valor: "$680,30",
          porcentaje: "+ 12.8%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
      },
    },
    {
      nombre: "Septiembre",
      fecha: "31/09/2024",
      indicadores: {
        "Activos totales": {
          valor: "$1,545,600",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Pasivos totales": {
          valor: "$685,300",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#FF6B8A",
        },
        Patrimonio: {
          valor: "$680,30",
          porcentaje: "+ 12.8%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Ratio de Liquidez": {
          valor: "$1,985,600",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Ratio de Endeudamiento": {
          valor: "$685,300",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#FF6B8A",
        },
        "Ratio 3": {
          valor: "$680,30",
          porcentaje: "+ 12.8%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
      },
    },
  ]);

  // Preparar datos para el gráfico de barras usando datos reales del backend
  const prepararDatosGrafico = () => {
    if (!datosFinancierosTransformados || datosFinancierosTransformados.length === 0) {
      return [];
    }

    // Tomar los últimos 6 periodos para el gráfico y revertir para mostrar cronológicamente
    const ultimosPeriodos = datosFinancierosTransformados.slice(0, 6).reverse();

    return ultimosPeriodos.map(periodo => {
      const agrupadores = periodo.agrupadores || [];

      // Buscar activos totales
      const activosTotales = agrupadores.find(agrupador =>
        agrupador.nombre_agrupador?.toLowerCase().includes('activo') &&
        agrupador.tipo_estado_financiero === 1 &&
        !agrupador.nombre_subagrupador
      );

      // Buscar pasivos totales
      const pasivosTotales = agrupadores.find(agrupador =>
        agrupador.nombre_agrupador?.toLowerCase().includes('pasivo') &&
        agrupador.tipo_estado_financiero === 1 &&
        !agrupador.nombre_subagrupador
      );

      // Buscar patrimonio
      const patrimonio = agrupadores.find(agrupador =>
        agrupador.nombre_agrupador?.toLowerCase().includes('patrimonio') &&
        agrupador.tipo_estado_financiero === 1
      );

      // Formatear nombre del periodo (extraer mes/año de la fecha)
      const fechaParts = periodo.fecha_fin.split('/');
      const nombrePeriodo = fechaParts.length >= 3 ?
        `${fechaParts[1]}/${fechaParts[2]}` : periodo.fecha_fin;

      return {
        nombre: nombrePeriodo,
        "Activos": activosTotales?.valor || 0,
        "Pasivos": Math.abs(pasivosTotales?.valor || 0), // Valor absoluto para visualización
        "Patrimonio": patrimonio?.valor || 0
      };
    });
  };
  const prepararDatosGraficoLineal = () => {
    // Tomar los últimos 12 meses para mostrar una tendencia más amplia
    const mesesHistoricos = datosMensuales.slice(0, 12).reverse();

    return mesesHistoricos.map(mes => {
      // Calcular algunos ratios financieros adicionales
      const activos = parseFloat(mes.indicadores["Activos totales"].valor.replace(/[$,]/g, ''));
      const pasivos = parseFloat(mes.indicadores["Pasivos totales"].valor.replace(/[$,]/g, ''));
      const patrimonio = parseFloat(mes.indicadores["Patrimonio"].valor.replace(/[$,]/g, ''));

      // Calcular ratios
      const endeudamiento = (pasivos / activos) * 100;
      const autonomia = (patrimonio / activos) * 100;

      return {
        nombre: mes.nombre,
        "Activos": activos / 1000, // Convertir a miles para mejor visualización
        "Pasivos": pasivos / 1000,
        "Patrimonio": patrimonio / 1000,
        "Ratio Endeudamiento": parseFloat(endeudamiento.toFixed(1)),
        "Ratio Autonomía": parseFloat(autonomia.toFixed(1))
      };
    });
  };
  // Formatear valores para el tooltip
  const formatoMoneda = (valor) => {
    return formatearNumero(valor, true, true);
  };
  // Formatear valores para el tooltip
  const formatoTooltip = (value, name) => {
    if (name.includes("Ratio")) {
      return [`${value}%`, name];
    }
    return [`${simboloMoneda}${value}k`, name];
  };

  // Función para formatear el valor de un ratio
  const formatearValorRatio = (valor, nombreRatio) => {
    if (valor === null || valor === undefined) return "N/A";

    // Definir qué ratios son montos (el resto son porcentajes)
    const ratiosMontos = ["Capital de Trabajo", "EBITDA"];
    const esRatioMonto = ratiosMontos.some(ratio => nombreRatio.toLowerCase().includes(ratio.toLowerCase()));

    if (esRatioMonto) {
      // Para ratios de monto, formatear con separadores de miles y símbolo de moneda
      return formatearNumero(valor, true, true);
    } else {
      // Para ratios de porcentaje, ya vienen multiplicados por 100 desde el backend
      // return `${valor.toFixed(4)}%`;
      // Mostrar solo los decimales que traer la variable valor
      // return `${valor}%`;
      /**  Si es diferente de cero aproximarlo a 2 decimales. Si aproximando a 2 decimales es cero, aproximar a 3 decimales.
        Si aproximando a 3 decimales es cero, aproximar a 4 decimales
        Si aproximando a 4 decimales es cero, mostrar el valor original */
      if (valor !== 0) {
        let valorAproximado = valor.toFixed(2);
        if (valorAproximado === "0.00" || valorAproximado === "-0.00") {
          valorAproximado = valor.toFixed(3);
          if (valorAproximado === "0.000" || valorAproximado === "-0.000") {
            valorAproximado = valor.toFixed(4);
            if (valorAproximado === "0.0000" || valorAproximado === "-0.0000") {
              valorAproximado = '0';
            }
          }
        }
        return `${valorAproximado}%`;
      } else {
        return `${valor}%`;
      }

    }
  };

  // Componente para mostrar un ratio individual con tooltip
  const RatioItem = ({ nombre, valor, formula }) => {
    const [showTooltip, setShowTooltip] = useState(false);

    return (
      <div className="flex flex-col w-full gap-1">
        <div className="flex justify-between w-full">
          <div className="flex justify-start items-center gap-2">
            <span className="text-[#1F263E] text-md font-base">
              {nombre}
            </span>
            <div
              className="relative"
              onMouseEnter={() => setShowTooltip(true)}
              onMouseLeave={() => setShowTooltip(false)}
            >
              <IconoInformacion size={"1rem"} color={"#979797"} />
              {showTooltip && (
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-sm rounded-lg shadow-lg z-10 whitespace-nowrap">
                  {formula}
                  <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
                </div>
              )}
            </div>
          </div>
          <span className="text-[#1F263E] text-md font-base">
            {formatearValorRatio(valor, nombre)}
          </span>
        </div>
        {/* <div className="flex items-center gap-2">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-[#FFD684] h-2 rounded-full"
              style={{
                width: `${(() => {
                  // Definir qué ratios son montos
                  const ratiosMontos = ["Capital de Trabajo", "EBITDA"];
                  const esRatioMonto = ratiosMontos.some(ratio => nombre.toLowerCase().includes(ratio.toLowerCase()));

                  if (esRatioMonto) {
                    // Para montos, usar una escala diferente (normalizar a un rango visible)
                    const valorAbsoluto = Math.abs(valor);
                    if (valorAbsoluto === 0) return 0;
                    // Escala logarítmica para montos grandes
                    const porcentaje = Math.min((Math.log10(valorAbsoluto + 1) / Math.log10(1000000)) * 100, 100);
                    return Math.max(porcentaje, 5); // Mínimo 5% para que sea visible
                  } else {
                    // Para porcentajes, usar el valor directamente (ya está en escala 0-100)
                    return Math.min(Math.abs(valor), 100);
                  }
                })()}%`
              }}
            ></div>
          </div>
        </div> */}
      </div>
    );
  };

  return (
    <div className="w-full h-full flex flex-col gap-4">
      <div className="flex w-full flex-col gap-4 justify-start items-start">
        <div
          className="flex gap-1 justify-start items-center cursor-pointer"
          onClick={() => navigate(RoutesPrivate.INICIO)}
        >
          <IconoRegresar
            size={"1.8rem"}
            color={"#909090"}
            salir={true}
            pagina={RoutesPrivate.INICIO}
          />
          <span className=" text-sm font-semibold text-[#909090]">
            Dashboard
          </span>
        </div>
        <div className="flex justify-between w-full">
          <span className="text-[#1F263E] text-2xl font-semibold">
            {tituloPeriodo}
          </span>
          <div className="flex gap-4 ">
            <select
              name="empresa"
              id="empresa"
              className="border border-gray-400 rounded-md p-1 min-w-[13rem] outline-none"
              value={empresaSeleccionada?.int_idEmpresa || ""}
              onChange={(e) => {
                const empresaId = parseInt(e.target.value);
                const empresa = empresas.find(emp => emp.int_idEmpresa === empresaId);
                handleEmpresaChange(empresa);
              }}
            >
              {cargandoEmpresas ? (
                <option value="" disabled>Cargando empresas...</option>
              ) : errorEmpresas ? (
                <option value="" disabled>Error al cargar empresas</option>
              ) : empresas.length === 0 ? (
                <option value="" disabled>No hay empresas disponibles</option>
              ) : (
                empresas.map((empresa) => (
                  <option key={empresa.int_idEmpresa} value={empresa.int_idEmpresa}>
                    {empresa.str_NombreEmpresa}
                  </option>
                ))
              )}
            </select>
            <select
              name="periodo"
              id="periodo"
              className="border border-gray-400 rounded-md p-1 min-w-[13rem] outline-none"
              value={periodoSeleccionado?.periodo_contable || ""}
              onChange={(e) => {
                const periodoContable = e.target.value;
                const periodo = periodos.find(p => p.periodo_contable === periodoContable);
                handlePeriodoChange(periodo);
              }}
            >
              {cargandoPeriodos ? (
                <option value="" disabled>Cargando periodos...</option>
              ) : errorPeriodos ? (
                <option value="" disabled>Error al cargar periodos</option>
              ) : periodos.length === 0 ? (
                <option value="" disabled>No hay periodos disponibles</option>
              ) : (
                periodos.map((periodo, index) => (
                  <option key={index} value={periodo.periodo_contable}>
                    {formatearNombrePeriodo(periodo, tipoPeriodo)}
                  </option>
                ))
              )}
            </select>
            <button
              className="group bg-[#FFFF] text-[#1F263E] p-2 rounded-lg border-[#E4E4E4] border-2 gap-2 flex items-center justify-center hover:bg-[#1F263E] hover:text-[#FFFF] cursor-pointer hover:border-[#1F263E]"
              onClick={navegarASimulacionesDesdeDetalleEF}
            >
              <IconoSimulaciones
                size={"1.2rem"}
                className="text-[#1F263E] group-hover:text-[#FFFF]"
              />{" "}
              Simulaciones
              <IconoFlechaSimular
                size={"1.2rem"}
                className="text-[#1F263E] group-hover:text-[#FFFF]"
              />
            </button>
          </div>
        </div>
      </div>
      <div className="flex w-full  justify-start items-start gap-4 box-border">
        <div className="flex flex-col justify-start items-start gap-4 max-w-[25%] w-full">
          <div className="flex flex-col justify-start items-start gap-4  w-full border-1 border-[#EFEFEF] p-4 rounded-lg">
            <span className="text-[#1F263E] text-lg font-base">
              Ratios Financieros
            </span>

            {/* Mostrar estado de carga o error */}
            {cargandoRatios ? (
              <div className="flex justify-center items-center w-full py-4">
                <span className="text-[#979797] text-sm">Cargando ratios financieros...</span>
              </div>
            ) : errorRatios ? (
              <div className="flex justify-center items-center w-full py-4">
                <span className="text-red-500 text-sm">{errorRatios}</span>
              </div>
            ) : ratiosFinancieros.length === 0 ? (
              <div className="flex justify-center items-center w-full py-4">
                <span className="text-[#979797] text-sm">No hay ratios financieros disponibles para este periodo</span>
              </div>
            ) : (
              /* Mostrar ratios dinámicamente */
              ratiosFinancieros.map((ratio, index) => (
                <RatioItem
                  key={index}
                  nombre={ratio.nombre_ratio}
                  valor={ratio.valor_ratio}
                  formula={ratio.formula_ratio}
                />
              ))
            )}
          </div>
          <div className="flex flex-col justify-start items-start gap-4  w-full border-1 border-[#EFEFEF] p-4 rounded-lg gap-3">
            <div className="flex justify-between w-full ">
              <span className="text-[#1F263E] text-lg font-base">
                Información General
              </span>
              <span className="text-[#979797] text-sm font-base flex gap-1 items-center">
                <IconoCalendar size={"0.8rem"} color={"#979797"} /> Subido:
                {fechaSubida || "No disponible"}
              </span>
            </div>
            <div className="flex justify-start items-center w-full">
              <span className="text-[#979797] text-md font-base min-w-[10rem]">
                Empresa:{" "}
              </span>
              <span className="text-[#1F263E] text-md font-base">
                {empresaSeleccionada?.str_NombreEmpresa || "No seleccionada"}
              </span>
            </div>
            <div className="flex justify-start items-center w-full">
              <span className="text-[#979797] text-md font-base min-w-[10rem]">
                NIF/CIF:
              </span>
              <span className="text-[#1F263E] text-md font-base">
                A-12345678
              </span>
            </div>
            <div className="flex justify-start items-center w-full">
              <span className="text-[#979797] text-md font-base min-w-[10rem]">
                Tipo de Periodo:{" "}
              </span>
              <span className="text-[#1F263E] text-md font-base">
                {tipoPeriodo === 1 ? "Anual" : tipoPeriodo === 2 ? "Trimestral" : tipoPeriodo === 3 ? "Mensual" : "No especificado"}
              </span>
            </div>
            <div className="flex justify-start items-center w-full">
              <span className="text-[#979797] text-md font-base min-w-[10rem]">
                Año Fiscal:{" "}
              </span>
              <span className="text-[#1F263E] text-md font-base">
                {fechaFin ? fechaFin.split('/')[2] : "2024"}
              </span>
            </div>
            <div className="flex justify-start items-center w-full">
              <span className="text-[#979797] text-md font-base min-w-[10rem]">
                Moneda:{" "}
              </span>
              <span className="text-[#1F263E] text-md font-base">{moneda} ({simboloMoneda})</span>
            </div>
            <div className="flex justify-start items-center w-full">
              <span className="text-[#979797] text-md font-base min-w-[10rem]">
                Normativa:{" "}
              </span>
              <span className="text-[#1F263E] text-md font-base">
                NIIF/IFRS
              </span>
            </div>
            <button className="flex gap-2 items-center justify-center w-[90%] m-auto bg-[#1F263E] border-none rounded-lg text-white p-2 cursor-pointer">
              <IconoVer size={"1rem"} color={"#FFFFFF"} /> Ver Documento
              Original
            </button>
          </div>
        </div>

        <div className="flex flex-col gap-2 max-w-[75%] w-full">
          <div className="flex gap-4 w-full flex-wrap justify-end items-center">
            {(() => {
              const datosActuales = obtenerDatosFinancierosActuales();

              return (
                <>
                  <div className="flex flex-col gap-1 border-1 border-[#EFEFEF] w-[calc(33.333%-1rem)] p-4 rounded-lg">
                    <span className="text-[#979797] text-xs font-semibold">
                      Activos Totales
                    </span>
                    <span className="text-[#1F263E] text-2xl font-semibold">
                      {cargandoDatosFinancieros ? "Cargando..." :
                       datosActuales.activosTotales !== null ?
                       formatearNumero(datosActuales.activosTotales, true, true) :
                       "N/A"}
                    </span>
                    <span className={`text-xs font-base ${
                      datosActuales.variacionActivos && datosActuales.variacionActivos.includes('+') ?
                      'text-[#31C969]' :
                      datosActuales.variacionActivos && datosActuales.variacionActivos.includes('-') ?
                      'text-[#FF6B8A]' :
                      'text-[#979797]'
                    }`}>
                      {datosActuales.variacionActivos || "Sin variación disponible"}
                      {datosActuales.variacionActivos ? " desde el último periodo" : ""}
                    </span>
                  </div>
                  <div className="flex flex-col gap-1 border-1 border-[#EFEFEF] w-[calc(33.333%-1rem)] p-4 rounded-lg">
                    <span className="text-[#979797] text-xs font-semibold">
                      Pasivos Totales
                    </span>
                    <span className="text-[#1F263E] text-2xl font-semibold">
                      {cargandoDatosFinancieros ? "Cargando..." :
                       datosActuales.pasivosTotales !== null ?
                       formatearNumero(Math.abs(datosActuales.pasivosTotales), true, true) :
                       "N/A"}
                    </span>
                    <span className={`text-xs font-base ${
                      datosActuales.variacionPasivos && datosActuales.variacionPasivos.includes('+') ?
                      'text-[#31C969]' :
                      datosActuales.variacionPasivos && datosActuales.variacionPasivos.includes('-') ?
                      'text-[#FF6B8A]' :
                      'text-[#979797]'
                    }`}>
                      {datosActuales.variacionPasivos || "Sin variación disponible"}
                      {datosActuales.variacionPasivos ? " desde el último periodo" : ""}
                    </span>
                  </div>
                  <div className="flex flex-col gap-1 border-1 border-[#EFEFEF] w-[calc(33.333%-1rem)] p-4 rounded-lg">
                    <span className="text-[#979797] text-xs font-semibold">
                      Patrimonio
                    </span>
                    <span className="text-[#1F263E] text-2xl font-semibold">
                      {cargandoDatosFinancieros ? "Cargando..." :
                       datosActuales.patrimonio !== null ?
                       formatearNumero(datosActuales.patrimonio, true, true) :
                       "N/A"}
                    </span>
                    <span className={`text-xs font-base ${
                      datosActuales.variacionPatrimonio && datosActuales.variacionPatrimonio.includes('+') ?
                      'text-[#31C969]' :
                      datosActuales.variacionPatrimonio && datosActuales.variacionPatrimonio.includes('-') ?
                      'text-[#FF6B8A]' :
                      'text-[#979797]'
                    }`}>
                      {datosActuales.variacionPatrimonio || "Sin variación disponible"}
                      {datosActuales.variacionPatrimonio ? " desde el último periodo" : ""}
                    </span>
                  </div>
                </>
              );
            })()}
          </div>

          {/* Gráfico de barras para comparar periodos */}
          <div className="w-full mt-6 border-1 border-[#EFEFEF] rounded-lg overflow-hidden">
            <div className="p-4 bg-[#F8FAFB]">
              <span className="text-[#1F263E] text-md font-semibold">
                Evolución Financiera por Periodo
              </span>
            </div>

            <div className="p-4">
              <div className="w-full h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={prepararDatosGrafico()}
                    margin={{ top: 20, right: 30, left: 20, bottom: 10 }}
                    barGap={8}
                    barSize={20}
                  >
                    <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="#EFEFEF" />
                    <XAxis
                      dataKey="nombre"
                      tick={{ fontSize: 12, fill: "#6B7280" }}
                      tickMargin={10}
                      axisLine={{ stroke: "#E5E7EB" }}
                    />
                    <YAxis
                      tick={{ fontSize: 12, fill: "#6B7280" }}
                      tickMargin={10}
                      axisLine={{ stroke: "#E5E7EB" }}
                      tickFormatter={(value) => `${simboloMoneda}${value/1000}k`}
                    />
                    <Tooltip
                      formatter={(value) => [formatoMoneda(value), ""]}
                      labelFormatter={(label) => `Periodo: ${label}`}
                      contentStyle={{
                        backgroundColor: '#fff',
                        border: '1px solid #E5E7EB',
                        borderRadius: '8px',
                        boxShadow: '0 2px 10px rgba(0,0,0,0.05)'
                      }}
                    />
                    <Legend
                      verticalAlign="bottom"
                      height={36}
                      iconType="circle"
                      wrapperStyle={{ paddingTop: '10px' }}
                    />
                    <Bar
                      dataKey="Activos"
                      name="Activos Totales"
                      fill="#4D96FF"
                      radius={[4, 4, 0, 0]}
                    />
                    <Bar
                      dataKey="Pasivos"
                      name="Pasivos Totales"
                      fill="#FF6B8A"
                      radius={[4, 4, 0, 0]}
                    />
                    <Bar
                      dataKey="Patrimonio"
                      name="Patrimonio"
                      fill="#21DDB8"
                      radius={[4, 4, 0, 0]}
                    />
                  </BarChart>
                </ResponsiveContainer>
              </div>


            </div>
          </div>

          {/* Gráfico lineal justo debajo del gráfico de barras */}
          <div className="p-4 bg-[#F8FAFB] border-t border-[#EFEFEF]">
            <span className="text-[#1F263E] text-md font-semibold">
              Tendencia de Crecimiento
            </span>
          </div>

          <div className="p-4">
            <div className="w-full h-[250px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={prepararDatosGrafico()}
                  margin={{ top: 20, right: 30, left: 20, bottom: 10 }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#EFEFEF" />
                  <XAxis
                    dataKey="nombre"
                    tick={{ fontSize: 12, fill: "#6B7280" }}
                    tickMargin={10}
                    axisLine={{ stroke: "#E5E7EB" }}
                  />
                  <YAxis
                    tick={{ fontSize: 12, fill: "#6B7280" }}
                    tickMargin={10}
                    axisLine={{ stroke: "#E5E7EB" }}
                    tickFormatter={(value) => `${simboloMoneda}${value/1000}k`}
                  />
                  <Tooltip
                    formatter={(value) => [formatoMoneda(value), ""]}
                    labelFormatter={(label) => `Periodo: ${label}`}
                    contentStyle={{
                      backgroundColor: '#fff',
                      border: '1px solid #E5E7EB',
                      borderRadius: '8px',
                      boxShadow: '0 2px 10px rgba(0,0,0,0.05)'
                    }}
                  />
                  <Legend
                    verticalAlign="bottom"
                    height={36}
                    iconType="circle"
                    wrapperStyle={{ paddingTop: '10px' }}
                  />
                  <Line
                    type="monotone"
                    dataKey="Activos"
                    name="Activos Totales"
                    stroke="#4D96FF"
                    strokeWidth={2}
                    dot={{ r: 4, fill: "#4D96FF" }}
                    activeDot={{ r: 6 }}
                  />
                  <Line
                    type="monotone"
                    dataKey="Pasivos"
                    name="Pasivos Totales"
                    stroke="#FF6B8A"
                    strokeWidth={2}
                    dot={{ r: 4, fill: "#FF6B8A" }}
                    activeDot={{ r: 6 }}
                  />
                  <Line
                    type="monotone"
                    dataKey="Patrimonio"
                    name="Patrimonio"
                    stroke="#21DDB8"
                    strokeWidth={2}
                    dot={{ r: 4, fill: "#21DDB8" }}
                    activeDot={{ r: 6 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>

          </div>
        </div>
      </div>
      <div className="flex w-full flex-col gap-4 justify-between items-center box-border border-1 border-[#EFEFEF] rounded-lg ">
        <div className="flex px-9 py-5 justify-between w-full bg-[#F8FAFB]">
          <span className="text-[#1F263E] text-md font-semibold">Activos</span>
          <span className="text-[#1F263E] text-md font-semibold">
            {(() => {
              const totales = obtenerTotalesDesdeDetallesCuentas();
              return cargandoDetallesCuentas ? "Cargando..." :
                     totales.activosTotales !== null ?
                     formatearNumero(totales.activosTotales, true, true) :
                     "N/A";
            })()}
          </span>
        </div>
        <div className="flex px-9 py-5 justify-between w-full bg-[#FFF] box-border gap-5">
          <div className="flex w-[49%] flex-col border-r border-[#E5E7EB] pr-4">
            <div className="flex flex-col w-full gap-1 mb-6">
              <span className="text-[#1F263E] text-md font-semibold">Activos Corrientes</span>
            </div>

            <div className="w-full">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="text-left">
                    <th className="py-3 text-sm font-base text-[#6B7280]">Concepto</th>
                    <th className="py-3 text-sm font-base text-[#6B7280] text-right">Valor</th>
                    <th className="py-3 text-sm font-base text-[#6B7280] text-right">AV</th>
                    <th className="py-3 text-sm font-base text-[#6B7280] text-right">AH</th>
                  </tr>
                </thead>
                <tbody>
                  {cargandoDetallesCuentas ? (
                    <tr>
                      <td colSpan="4" className="py-4 text-center text-sm text-[#979797]">
                        Cargando cuentas...
                      </td>
                    </tr>
                  ) : errorDetallesCuentas ? (
                    <tr>
                      <td colSpan="4" className="py-4 text-center text-sm text-red-500">
                        {errorDetallesCuentas}
                      </td>
                    </tr>
                  ) : (
                    (() => {
                      const cuentasActivoCorriente = obtenerCuentasPorAgrupador("activo corriente");
                      const datosAgrupador = obtenerDatosAgrupador("activo corriente");

                      if (cuentasActivoCorriente.length === 0) {
                        return (
                          <tr>
                            <td colSpan="4" className="py-4 text-center text-sm text-[#979797]">
                              No hay cuentas disponibles
                            </td>
                          </tr>
                        );
                      }

                      const filasCuentas = cuentasActivoCorriente.map((cuenta, index) => (
                        <tr key={index} className="border-t border-[#E5E7EB]">
                          <td className="py-4 text-sm font-base text-[#1F263E]">
                            {cuenta.nombre_cuenta}
                          </td>
                          <td className="py-4 text-sm font-base text-[#1F263E] text-right">
                            {formatearNumero(cuenta.valor_cuenta, true, true)}
                          </td>
                          <td className="py-4 text-sm font-base text-[#31C969] text-right">
                            {cuenta.proporcion !== null ? formatearProporcion(cuenta.proporcion) : "N/A"}
                          </td>
                          <td className="py-4 text-sm font-base text-right">
                            {cuenta.variacion !== null ? (
                              <span className={cuenta.variacion >= 0 ? "text-[#31C969]" : "text-[#FF6B8A]"}>
                                {formatearVariacion(cuenta.variacion)}
                              </span>
                            ) : (
                              <span className="text-[#979797]">N/A</span>
                            )}
                          </td>
                        </tr>
                      ));

                      // Agregar fila de total si existe el agrupador
                      if (datosAgrupador) {
                        filasCuentas.push(
                          <tr key="total" className="border-t-2 border-[#E5E7EB] font-semibold bg-[#F9FAFB]">
                            <td className="py-4 text-sm text-[#1F263E]">
                              Total Activos Corrientes
                            </td>
                            <td className="py-4 text-sm text-[#1F263E] text-right">
                              {formatearNumero(datosAgrupador.valor, true, true)}
                            </td>
                            <td className="py-4 text-sm text-[#31C969] text-right">
                              {datosAgrupador.proporcion !== null ? formatearProporcion(datosAgrupador.proporcion) : "N/A"}
                            </td>
                            <td className="py-4 text-sm text-right">
                              {datosAgrupador.variacion !== null ? (
                                <span className={datosAgrupador.variacion >= 0 ? "text-[#31C969]" : "text-[#FF6B8A]"}>
                                  {formatearVariacion(datosAgrupador.variacion)}
                                </span>
                              ) : (
                                <span className="text-[#979797]">N/A</span>
                              )}
                            </td>
                          </tr>
                        );
                      }

                      return filasCuentas;
                    })()
                  )}
                </tbody>
              </table>
            </div>
          </div>
          <div className="flex w-[49%] flex-col pl-4">
            <div className="flex flex-col w-full gap-1 mb-6">
              <span className="text-[#1F263E] text-md font-semibold">Activos No Corrientes</span>
            </div>

            <div className="w-full">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="text-left">
                    <th className="py-3 text-sm font-base text-[#6B7280]">Concepto</th>
                    <th className="py-3 text-sm font-base text-[#6B7280] text-right">Valor</th>
                    <th className="py-3 text-sm font-base text-[#6B7280] text-right">AV</th>
                    <th className="py-3 text-sm font-base text-[#6B7280] text-right">AH</th>
                  </tr>
                </thead>
                <tbody>
                  {cargandoDetallesCuentas ? (
                    <tr>
                      <td colSpan="4" className="py-4 text-center text-sm text-[#979797]">
                        Cargando cuentas...
                      </td>
                    </tr>
                  ) : errorDetallesCuentas ? (
                    <tr>
                      <td colSpan="4" className="py-4 text-center text-sm text-red-500">
                        {errorDetallesCuentas}
                      </td>
                    </tr>
                  ) : (
                    (() => {
                      const cuentasActivoNoCorriente = obtenerCuentasPorAgrupador("activo no corriente");
                      const datosAgrupador = obtenerDatosAgrupador("activo no corriente");

                      if (cuentasActivoNoCorriente.length === 0) {
                        return (
                          <tr>
                            <td colSpan="4" className="py-4 text-center text-sm text-[#979797]">
                              No hay cuentas disponibles
                            </td>
                          </tr>
                        );
                      }

                      const filasCuentas = cuentasActivoNoCorriente.map((cuenta, index) => (
                        <tr key={index} className="border-t border-[#E5E7EB]">
                          <td className="py-4 text-sm font-base text-[#1F263E]">
                            {cuenta.nombre_cuenta}
                          </td>
                          <td className="py-4 text-sm font-base text-[#1F263E] text-right">
                            {formatearNumero(cuenta.valor_cuenta, true, true)}
                          </td>
                          <td className="py-4 text-sm font-base text-[#31C969] text-right">
                            {cuenta.proporcion !== null ? formatearProporcion(cuenta.proporcion) : "N/A"}
                          </td>
                          <td className="py-4 text-sm font-base text-right">
                            {cuenta.variacion !== null ? (
                              <span className={cuenta.variacion >= 0 ? "text-[#31C969]" : "text-[#FF6B8A]"}>
                                {formatearVariacion(cuenta.variacion)}
                              </span>
                            ) : (
                              <span className="text-[#979797]">N/A</span>
                            )}
                          </td>
                        </tr>
                      ));

                      // Agregar fila de total si existe el agrupador
                      if (datosAgrupador) {
                        filasCuentas.push(
                          <tr key="total" className="border-t-2 border-[#E5E7EB] font-semibold bg-[#F9FAFB]">
                            <td className="py-4 text-sm text-[#1F263E]">
                              Total Activos No Corrientes
                            </td>
                            <td className="py-4 text-sm text-[#1F263E] text-right">
                              {formatearNumero(datosAgrupador.valor, true, true)}
                            </td>
                            <td className="py-4 text-sm text-[#31C969] text-right">
                              {datosAgrupador.proporcion !== null ? formatearProporcion(datosAgrupador.proporcion) : "N/A"}
                            </td>
                            <td className="py-4 text-sm text-right">
                              {datosAgrupador.variacion !== null ? (
                                <span className={datosAgrupador.variacion >= 0 ? "text-[#31C969]" : "text-[#FF6B8A]"}>
                                  {formatearVariacion(datosAgrupador.variacion)}
                                </span>
                              ) : (
                                <span className="text-[#979797]">N/A</span>
                              )}
                            </td>
                          </tr>
                        );
                      }

                      return filasCuentas;
                    })()
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      <div className="flex w-full flex-col gap-4 justify-between items-center box-border border-1 border-[#EFEFEF] rounded-lg ">
        <div className="flex px-9 py-5 justify-between w-full bg-[#F8FAFB]">
          <span className="text-[#1F263E] text-md font-semibold">Pasivos</span>
          <span className="text-[#1F263E] text-md font-semibold">
            {(() => {
              const totales = obtenerTotalesDesdeDetallesCuentas();
              return cargandoDetallesCuentas ? "Cargando..." :
                     totales.pasivosTotales !== null ?
                     formatearNumero(Math.abs(totales.pasivosTotales), true, true) :
                     "N/A";
            })()}
          </span>
        </div>
        <div className="flex px-9 py-5 justify-between w-full bg-[#FFF] box-border gap-5">
          <div className="flex w-[49%] flex-col border-r border-[#E5E7EB] pr-4">
            <div className="flex flex-col w-full gap-1 mb-6">
              <span className="text-[#1F263E] text-md font-semibold">Pasivos Corrientes</span>
            </div>

            <div className="w-full">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="text-left">
                    <th className="py-3 text-sm font-base text-[#6B7280]">Concepto</th>
                    <th className="py-3 text-sm font-base text-[#6B7280] text-right">Valor</th>
                    <th className="py-3 text-sm font-base text-[#6B7280] text-right">AV</th>
                    <th className="py-3 text-sm font-base text-[#6B7280] text-right">AH</th>
                  </tr>
                </thead>
                <tbody>
                  {cargandoDetallesCuentas ? (
                    <tr>
                      <td colSpan="4" className="py-4 text-center text-sm text-[#979797]">
                        Cargando cuentas...
                      </td>
                    </tr>
                  ) : errorDetallesCuentas ? (
                    <tr>
                      <td colSpan="4" className="py-4 text-center text-sm text-red-500">
                        {errorDetallesCuentas}
                      </td>
                    </tr>
                  ) : (
                    (() => {
                      const cuentasPasivoCorriente = obtenerCuentasPorAgrupador("pasivo corriente");
                      const datosAgrupador = obtenerDatosAgrupador("pasivo corriente");

                      if (cuentasPasivoCorriente.length === 0) {
                        return (
                          <tr>
                            <td colSpan="4" className="py-4 text-center text-sm text-[#979797]">
                              No hay cuentas disponibles
                            </td>
                          </tr>
                        );
                      }

                      const filasCuentas = cuentasPasivoCorriente.map((cuenta, index) => (
                        <tr key={index} className="border-t border-[#E5E7EB]">
                          <td className="py-4 text-sm font-base text-[#1F263E]">
                            {cuenta.nombre_cuenta}
                          </td>
                          <td className="py-4 text-sm font-base text-[#1F263E] text-right">
                            {formatearNumero(cuenta.valor_cuenta, true, true)}
                          </td>
                          <td className="py-4 text-sm font-base text-[#31C969] text-right">
                            {cuenta.proporcion !== null ? formatearProporcion(cuenta.proporcion) : "N/A"}
                          </td>
                          <td className="py-4 text-sm font-base text-right">
                            {cuenta.variacion !== null ? (
                              <span className={cuenta.variacion >= 0 ? "text-[#31C969]" : "text-[#FF6B8A]"}>
                                {formatearVariacion(cuenta.variacion)}
                              </span>
                            ) : (
                              <span className="text-[#979797]">N/A</span>
                            )}
                          </td>
                        </tr>
                      ));

                      // Agregar fila de total si existe el agrupador
                      if (datosAgrupador) {
                        filasCuentas.push(
                          <tr key="total" className="border-t-2 border-[#E5E7EB] font-semibold bg-[#F9FAFB]">
                            <td className="py-4 text-sm text-[#1F263E]">
                              Total Pasivos Corrientes
                            </td>
                            <td className="py-4 text-sm text-[#1F263E] text-right">
                              {formatearNumero(datosAgrupador.valor, true, true)}
                            </td>
                            <td className="py-4 text-sm text-[#31C969] text-right">
                              {datosAgrupador.proporcion !== null ? formatearProporcion(datosAgrupador.proporcion) : "N/A"}
                            </td>
                            <td className="py-4 text-sm text-right">
                              {datosAgrupador.variacion !== null ? (
                                <span className={datosAgrupador.variacion >= 0 ? "text-[#31C969]" : "text-[#FF6B8A]"}>
                                  {formatearVariacion(datosAgrupador.variacion)}
                                </span>
                              ) : (
                                <span className="text-[#979797]">N/A</span>
                              )}
                            </td>
                          </tr>
                        );
                      }

                      return filasCuentas;
                    })()
                  )}
                </tbody>
              </table>
            </div>
          </div>
          <div className="flex w-[49%] flex-col pl-4">
            <div className="flex flex-col w-full gap-1 mb-6">
              <span className="text-[#1F263E] text-md font-semibold">Pasivos No Corrientes</span>
            </div>

            <div className="w-full">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="text-left">
                    <th className="py-3 text-sm font-base text-[#6B7280]">Concepto</th>
                    <th className="py-3 text-sm font-base text-[#6B7280] text-right">Valor</th>
                    <th className="py-3 text-sm font-base text-[#6B7280] text-right">AV</th>
                    <th className="py-3 text-sm font-base text-[#6B7280] text-right">AH</th>
                  </tr>
                </thead>
                <tbody>
                  {cargandoDetallesCuentas ? (
                    <tr>
                      <td colSpan="4" className="py-4 text-center text-sm text-[#979797]">
                        Cargando cuentas...
                      </td>
                    </tr>
                  ) : errorDetallesCuentas ? (
                    <tr>
                      <td colSpan="4" className="py-4 text-center text-sm text-red-500">
                        {errorDetallesCuentas}
                      </td>
                    </tr>
                  ) : (
                    (() => {
                      const cuentasPasivoNoCorriente = obtenerCuentasPorAgrupador("pasivo no corriente");
                      const datosAgrupador = obtenerDatosAgrupador("pasivo no corriente");

                      if (cuentasPasivoNoCorriente.length === 0) {
                        return (
                          <tr>
                            <td colSpan="4" className="py-4 text-center text-sm text-[#979797]">
                              No hay cuentas disponibles
                            </td>
                          </tr>
                        );
                      }

                      const filasCuentas = cuentasPasivoNoCorriente.map((cuenta, index) => (
                        <tr key={index} className="border-t border-[#E5E7EB]">
                          <td className="py-4 text-sm font-base text-[#1F263E]">
                            {cuenta.nombre_cuenta}
                          </td>
                          <td className="py-4 text-sm font-base text-[#1F263E] text-right">
                            {formatearNumero(cuenta.valor_cuenta, true, true)}
                          </td>
                          <td className="py-4 text-sm font-base text-[#31C969] text-right">
                            {cuenta.proporcion !== null ? formatearProporcion(cuenta.proporcion) : "N/A"}
                          </td>
                          <td className="py-4 text-sm font-base text-right">
                            {cuenta.variacion !== null ? (
                              <span className={cuenta.variacion >= 0 ? "text-[#31C969]" : "text-[#FF6B8A]"}>
                                {formatearVariacion(cuenta.variacion)}
                              </span>
                            ) : (
                              <span className="text-[#979797]">N/A</span>
                            )}
                          </td>
                        </tr>
                      ));

                      // Agregar fila de total si existe el agrupador
                      if (datosAgrupador) {
                        filasCuentas.push(
                          <tr key="total" className="border-t-2 border-[#E5E7EB] font-semibold bg-[#F9FAFB]">
                            <td className="py-4 text-sm text-[#1F263E]">
                              Total Pasivos No Corrientes
                            </td>
                            <td className="py-4 text-sm text-[#1F263E] text-right">
                              {formatearNumero(datosAgrupador.valor, true, true)}
                            </td>
                            <td className="py-4 text-sm text-[#31C969] text-right">
                              {datosAgrupador.proporcion !== null ? formatearProporcion(datosAgrupador.proporcion) : "N/A"}
                            </td>
                            <td className="py-4 text-sm text-right">
                              {datosAgrupador.variacion !== null ? (
                                <span className={datosAgrupador.variacion >= 0 ? "text-[#31C969]" : "text-[#FF6B8A]"}>
                                  {formatearVariacion(datosAgrupador.variacion)}
                                </span>
                              ) : (
                                <span className="text-[#979797]">N/A</span>
                              )}
                            </td>
                          </tr>
                        );
                      }

                      return filasCuentas;
                    })()
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      <div className="flex w-1/2 flex-col gap-4 justify-between items-center box-border border-1 border-[#EFEFEF] rounded-lg">
        <div className="flex px-9 py-5 justify-between w-full bg-[#F8FAFB]">
          <span className="text-[#1F263E] text-md font-semibold">Patrimonio</span>
          <span className="text-[#1F263E] text-md font-semibold">
            {(() => {
              const totales = obtenerTotalesDesdeDetallesCuentas();
              return cargandoDetallesCuentas ? "Cargando..." :
                     totales.patrimonio !== null ?
                     formatearNumero(totales.patrimonio, true, true) :
                     "N/A";
            })()}
          </span>
        </div>
        <div className="flex px-9 py-5 justify-between w-full bg-[#FFF] box-border">
          <div className="flex w-full flex-col">
            <div className="w-full">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="text-left">
                    <th className="py-3 text-sm font-base text-[#6B7280]">Concepto</th>
                    <th className="py-3 text-sm font-base text-[#6B7280] text-right">Valor</th>
                    <th className="py-3 text-sm font-base text-[#6B7280] text-right">AV</th>
                    <th className="py-3 text-sm font-base text-[#6B7280] text-right">AH</th>
                  </tr>
                </thead>
                <tbody>
                  {cargandoDetallesCuentas ? (
                    <tr>
                      <td colSpan="4" className="py-4 text-center text-sm text-[#979797]">
                        Cargando cuentas...
                      </td>
                    </tr>
                  ) : errorDetallesCuentas ? (
                    <tr>
                      <td colSpan="4" className="py-4 text-center text-sm text-red-500">
                        {errorDetallesCuentas}
                      </td>
                    </tr>
                  ) : (
                    (() => {
                      const cuentasPatrimonio = obtenerCuentasPorAgrupador("patrimonio");
                      const datosAgrupador = obtenerDatosAgrupador("patrimonio");

                      if (cuentasPatrimonio.length === 0) {
                        return (
                          <tr>
                            <td colSpan="4" className="py-4 text-center text-sm text-[#979797]">
                              No hay cuentas disponibles
                            </td>
                          </tr>
                        );
                      }

                      const filasCuentas = cuentasPatrimonio.map((cuenta, index) => (
                        <tr key={index} className="border-t border-[#E5E7EB]">
                          <td className="py-4 text-sm font-base text-[#1F263E]">
                            {cuenta.nombre_cuenta}
                          </td>
                          <td className="py-4 text-sm font-base text-[#1F263E] text-right">
                            {formatearNumero(cuenta.valor_cuenta, true, true)}
                          </td>
                          <td className="py-4 text-sm font-base text-[#31C969] text-right">
                            {cuenta.proporcion !== null ? formatearProporcion(cuenta.proporcion) : "N/A"}
                          </td>
                          <td className="py-4 text-sm font-base text-right">
                            {cuenta.variacion !== null ? (
                              <span className={cuenta.variacion >= 0 ? "text-[#31C969]" : "text-[#FF6B8A]"}>
                                {formatearVariacion(cuenta.variacion)}
                              </span>
                            ) : (
                              <span className="text-[#979797]">N/A</span>
                            )}
                          </td>
                        </tr>
                      ));

                      // Agregar fila de total si existe el agrupador
                      if (datosAgrupador) {
                        filasCuentas.push(
                          <tr key="total" className="border-t-2 border-[#E5E7EB] font-semibold bg-[#F9FAFB]">
                            <td className="py-4 text-sm text-[#1F263E]">
                              Total Patrimonio
                            </td>
                            <td className="py-4 text-sm text-[#1F263E] text-right">
                              {formatearNumero(datosAgrupador.valor, true, true)}
                            </td>
                            <td className="py-4 text-sm text-[#31C969] text-right">
                              {datosAgrupador.proporcion !== null ? formatearProporcion(datosAgrupador.proporcion) : "N/A"}
                            </td>
                            <td className="py-4 text-sm text-right">
                              {datosAgrupador.variacion !== null ? (
                                <span className={datosAgrupador.variacion >= 0 ? "text-[#31C969]" : "text-[#FF6B8A]"}>
                                  {formatearVariacion(datosAgrupador.variacion)}
                                </span>
                              ) : (
                                <span className="text-[#979797]">N/A</span>
                              )}
                            </td>
                          </tr>
                        );
                      }

                      return filasCuentas;
                    })()
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      <div className="flex w-full gap-4 justify-between items-center box-border border-1 border-[#EFEFEF] rounded-lg p-5">
        <div className="flex w-full flex-col  p-4">
          <div className="flex flex-col w-full gap-1 mb-6">
            <span className="text-[#1F263E] text-md font-semibold">
              Análisis de Composición
            </span>
            <span className="text-[#8A8A8A] text-sm font-base">
              Distribución de activos, pasivos y patrimonio
            </span>
          </div>

          <div className="flex flex-col w-full gap-5">
            {(() => {
              const composicion = calcularComposicionFinanciera();

              return (
                <>
                  {/* Comparativa de Activos */}
                  <div className="flex flex-col gap-2">
                    <span className="text-[#1F263E] text-sm font-medium">
                      Comparativa de Activos
                    </span>
                    {cargandoDatosFinancieros ? (
                      <div className="w-full h-8 bg-gray-200 rounded-md flex items-center justify-center">
                        <span className="text-xs text-gray-500">Cargando...</span>
                      </div>
                    ) : errorDatosFinancieros ? (
                      <div className="w-full h-8 bg-red-100 rounded-md flex items-center justify-center">
                        <span className="text-xs text-red-500">Error al cargar datos</span>
                      </div>
                    ) : (
                      <div className="w-full h-8 bg-white rounded-md overflow-hidden">
                        <div className="flex h-full">
                          <div
                            className="bg-[#4D96FF] h-full"
                            style={{ width: `${composicion.porcentajeActivoCorriente}%` }}
                          ></div>
                          <div
                            className="bg-[#B8D7FF] h-full"
                            style={{ width: `${composicion.porcentajeActivoNoCorriente}%` }}
                          ></div>
                        </div>
                      </div>
                    )}
                    <div className="flex gap-6 mt-1">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full bg-[#4D96FF]"></div>
                        <span className="text-[#1F263E] text-xs">
                          Activos corrientes ({composicion.porcentajeActivoCorriente.toFixed(1)}%)
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full bg-[#B8D7FF]"></div>
                        <span className="text-[#1F263E] text-xs">
                          Activos no corrientes ({composicion.porcentajeActivoNoCorriente.toFixed(1)}%)
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Estructura Financiera */}
                  <div className="flex flex-col gap-2 mt-4">
                    <span className="text-[#1F263E] text-sm font-medium">
                      Estructura Financiera
                    </span>
                    {cargandoDatosFinancieros ? (
                      <div className="w-full h-8 bg-gray-200 rounded-md flex items-center justify-center">
                        <span className="text-xs text-gray-500">Cargando...</span>
                      </div>
                    ) : errorDatosFinancieros ? (
                      <div className="w-full h-8 bg-red-100 rounded-md flex items-center justify-center">
                        <span className="text-xs text-red-500">Error al cargar datos</span>
                      </div>
                    ) : (
                      <div className="w-full h-8 bg-white rounded-md overflow-hidden">
                        <div className="flex h-full">
                          <div
                            className="bg-[#FFA26B] h-full"
                            style={{ width: `${composicion.porcentajePasivoCorriente}%` }}
                          ></div>
                          <div
                            className="bg-[#E05E00] h-full"
                            style={{ width: `${composicion.porcentajePasivoNoCorriente}%` }}
                          ></div>
                          <div
                            className="bg-[#4CD964] h-full"
                            style={{ width: `${composicion.porcentajePatrimonio}%` }}
                          ></div>
                        </div>
                      </div>
                    )}
                    <div className="flex flex-wrap gap-6 mt-1">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full bg-[#FFA26B]"></div>
                        <span className="text-[#1F263E] text-xs">
                          Pasivos corrientes ({composicion.porcentajePasivoCorriente.toFixed(1)}%)
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full bg-[#E05E00]"></div>
                        <span className="text-[#1F263E] text-xs">
                          Pasivos no corrientes ({composicion.porcentajePasivoNoCorriente.toFixed(1)}%)
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full bg-[#4CD964]"></div>
                        <span className="text-[#1F263E] text-xs">
                          Patrimonio ({composicion.porcentajePatrimonio.toFixed(1)}%)
                        </span>
                      </div>
                    </div>
                  </div>
                </>
              );
            })()}
          </div>
        </div>
      </div>

    </div>
  );
};

export default DetalleEF;
