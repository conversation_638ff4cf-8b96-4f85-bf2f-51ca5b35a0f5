import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import IconoFlechaSimular from "../../../assets/SVG/IconoFlechaSimular";
import IconoSimulaciones from "../../../assets/SVG/IconoSimulaciones";
import IconoRegresar from "../../../assets/SVG/IconoRegresar";
import IconoInformacion from "../../../assets/SVG/IconoInformacion";
import IconoReestablecer from "../../../assets/SVG/IconoReestablecer";
import { RoutesPrivate } from "../../../Routes/ProtectedRoute";
import { formatearFechaDDMMYYYY } from "../../../utils/dateUtils";

const Simulaciones = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // Recibir parámetros desde Inicio.jsx
  const parametrosInicio = location.state || {};
  const {
    tipoPeriodo: tipoPeriodoInicio,
    fechaFinPeriodo: fechaFinPeriodoInicio,
    idEstadoFinanciero: idEstadoFinancieroInicio
  } = parametrosInicio;

  // Estados para almacenar los parámetros recibidos
  const [tipoPeriodoActual, setTipoPeriodoActual] = useState(tipoPeriodoInicio || null);
  const [fechaFinActual, setFechaFinActual] = useState(fechaFinPeriodoInicio || null);
  const [idEstadoFinancieroActual, setIdEstadoFinancieroActual] = useState(idEstadoFinancieroInicio || null);

  const [datosMensuales, setDatosMensuales] = useState([
    {
      nombre: "Abril",
      fecha: "31/04/2025",
      indicadores: {
        "Activos totales": {
          valor: "$1,245,600",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Pasivos totales": {
          valor: "$645,300",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#FF6B8A",
        },
        Patrimonio: {
          valor: "$600,30",
          porcentaje: "+ 12.8%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Ratio de Liquidez": {
          valor: "$1,245,600",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Ratio de Endeudamiento": {
          valor: "$645,300",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#FF6B8A",
        },
        "Ratio 3": {
          valor: "$600,30",
          porcentaje: "+ 12.8%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
      },
    },
    {
      nombre: "Marzo",
      fecha: "31/03/2025",
      indicadores: {
        "Activos totales": {
          valor: "$1,245,600",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Pasivos totales": {
          valor: "$645,300",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#FF6B8A",
        },
        Patrimonio: {
          valor: "$600,30",
          porcentaje: "+ 12.8%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Ratio de Liquidez": {
          valor: "$1,245,600",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Ratio de Endeudamiento": {
          valor: "$645,300",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#FF6B8A",
        },
        "Ratio 3": {
          valor: "$600,30",
          porcentaje: "+ 12.8%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
      },
    },
    {
      nombre: "Febrero",
      fecha: "31/02/2025",
      indicadores: {
        "Activos totales": {
          valor: "$1,245,600",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Pasivos totales": {
          valor: "$645,300",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#FF6B8A",
        },
        Patrimonio: {
          valor: "$600,30",
          porcentaje: "+ 12.8%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Ratio de Liquidez": {
          valor: "$1,245,600",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Ratio de Endeudamiento": {
          valor: "$645,300",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#FF6B8A",
        },
        "Ratio 3": {
          valor: "$600,30",
          porcentaje: "+ 12.8%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
      },
    },
    {
      nombre: "Enero",
      fecha: "31/01/2025",
      indicadores: {
        "Activos totales": {
          valor: "$1,545,600",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Pasivos totales": {
          valor: "$685,300",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#FF6B8A",
        },
        Patrimonio: {
          valor: "$680,30",
          porcentaje: "+ 12.8%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Ratio de Liquidez": {
          valor: "$1,985,600",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Ratio de Endeudamiento": {
          valor: "$685,300",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#FF6B8A",
        },
        "Ratio 3": {
          valor: "$680,30",
          porcentaje: "+ 12.8%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
      },
    },
    {
      nombre: "Diciembre",
      fecha: "31/12/2024",
      indicadores: {
        "Activos totales": {
          valor: "$1,545,600",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Pasivos totales": {
          valor: "$685,300",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#FF6B8A",
        },
        Patrimonio: {
          valor: "$680,30",
          porcentaje: "+ 12.8%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Ratio de Liquidez": {
          valor: "$1,985,600",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Ratio de Endeudamiento": {
          valor: "$685,300",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#FF6B8A",
        },
        "Ratio 3": {
          valor: "$680,30",
          porcentaje: "+ 12.8%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
      },
    },
    {
      nombre: "Noviembre",
      fecha: "31/11/2024",
      indicadores: {
        "Activos totales": {
          valor: "$1,545,600",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Pasivos totales": {
          valor: "$685,300",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#FF6B8A",
        },
        Patrimonio: {
          valor: "$680,30",
          porcentaje: "+ 12.8%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Ratio de Liquidez": {
          valor: "$1,985,600",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Ratio de Endeudamiento": {
          valor: "$685,300",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#FF6B8A",
        },
        "Ratio 3": {
          valor: "$680,30",
          porcentaje: "+ 12.8%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
      },
    },
    {
      nombre: "Octubre",
      fecha: "31/10/2024",
      indicadores: {
        "Activos totales": {
          valor: "$1,545,600",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Pasivos totales": {
          valor: "$685,300",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#FF6B8A",
        },
        Patrimonio: {
          valor: "$680,30",
          porcentaje: "+ 12.8%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Ratio de Liquidez": {
          valor: "$1,985,600",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Ratio de Endeudamiento": {
          valor: "$685,300",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#FF6B8A",
        },
        "Ratio 3": {
          valor: "$680,30",
          porcentaje: "+ 12.8%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
      },
    },
    {
      nombre: "Septiembre",
      fecha: "31/09/2024",
      indicadores: {
        "Activos totales": {
          valor: "$1,545,600",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Pasivos totales": {
          valor: "$685,300",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#FF6B8A",
        },
        Patrimonio: {
          valor: "$680,30",
          porcentaje: "+ 12.8%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Ratio de Liquidez": {
          valor: "$1,985,600",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
        "Ratio de Endeudamiento": {
          valor: "$685,300",
          porcentaje: "+ 2.1%",
          tendencia: "positiva",
          colorGrafico: "#FF6B8A",
        },
        "Ratio 3": {
          valor: "$680,30",
          porcentaje: "+ 12.8%",
          tendencia: "positiva",
          colorGrafico: "#21DDB8",
        },
      },
    },
  ]);

  // Valores iniciales para la simulación DuPont
  const [utilidadNeta, setUtilidadNeta] = useState(32000000);
  const [utilidadNetaSlider, setUtilidadNetaSlider] = useState(50);
  const [ventas, setVentas] = useState(230342200);
  const [ventasSlider, setVentasSlider] = useState(50);
  const [activos, setActivos] = useState(50000000);
  const [activosSlider, setActivosSlider] = useState(50);
  const [patrimonio, setPatrimonio] = useState(23400000);
  const [patrimonioSlider, setPatrimonioSlider] = useState(50);

  // Calcular ROE
  const margenNeto = utilidadNeta / ventas;
  const rotacionActivos = ventas / activos;
  const multiplicadorCapital = activos / patrimonio;
  const roeSimulado = margenNeto * rotacionActivos * multiplicadorCapital * 100;
  const roeReal = 31.8; // Valor fijo para comparación

  // Añadir historial de cambios para el gráfico
  const [historialCambios, setHistorialCambios] = useState([
    {
      nombre: "Valores iniciales",
      utilidadNeta: 32000000,
      ventas: 230342200,
      activos: 50000000,
      patrimonio: 23400000,
      roe: 31.8
    }
  ]);

  // useEffect para mostrar los parámetros recibidos desde otras páginas
  useEffect(() => {
    if (tipoPeriodoInicio || fechaFinPeriodoInicio || idEstadoFinancieroInicio) {
      console.log("=== PARÁMETROS RECIBIDOS EN SIMULACIONES ===");
      console.log("Tipo de Periodo:", tipoPeriodoInicio, tipoPeriodoInicio === 1 ? "(Anual)" : tipoPeriodoInicio === 3 ? "(Mensual)" : "(Otro)");
      console.log("Fecha Fin de Periodo:", fechaFinPeriodoInicio);
      console.log("ID Estado Financiero Tipo 1:", idEstadoFinancieroInicio);
      console.log("Origen: Puede ser desde Inicio.jsx o DetalleEF.jsx");
      console.log("===============================================");
    } else {
      console.log("No se recibieron parámetros desde otras páginas");
    }
  }, []);

  // Actualizar historial cuando cambian los valores
  useEffect(() => {
    // Solo añadir al historial si los valores son diferentes al último registro
    const ultimoRegistro = historialCambios[historialCambios.length - 1];
    if (
      ultimoRegistro.utilidadNeta !== utilidadNeta ||
      ultimoRegistro.ventas !== ventas ||
      ultimoRegistro.activos !== activos ||
      ultimoRegistro.patrimonio !== patrimonio
    ) {
      setHistorialCambios([
        ...historialCambios,
        {
          nombre: `Simulación ${historialCambios.length}`,
          utilidadNeta,
          ventas,
          activos,
          patrimonio,
          roe: parseFloat(roeSimulado.toFixed(2))
        }
      ]);
    }
  }, [utilidadNeta, ventas, activos, patrimonio]);

  // Preparar datos para el gráfico
  const prepararDatosGrafico = () => {
    // Normalizar valores para que sean comparables en el gráfico
    return historialCambios.map(registro => ({
      nombre: registro.nombre,
      "Utilidad Neta": (registro.utilidadNeta / 1000000).toFixed(1),
      "Ventas": (registro.ventas / 10000000).toFixed(1),
      "Activos": (registro.activos / 1000000).toFixed(1),
      "Patrimonio": (registro.patrimonio / 1000000).toFixed(1),
      "ROE": registro.roe
    }));
  };

  // Funciones para manejar cambios en los sliders
  const handleUtilidadNetaChange = (e) => {
    const value = parseInt(e.target.value);
    setUtilidadNetaSlider(value);
    setUtilidadNeta(20000000 + (value * 400000));
  };

  const handleVentasChange = (e) => {
    const value = parseInt(e.target.value);
    setVentasSlider(value);
    setVentas(150000000 + (value * 2000000));
  };

  const handleActivosChange = (e) => {
    const value = parseInt(e.target.value);
    setActivosSlider(value);
    setActivos(30000000 + (value * 500000));
  };

  const handlePatrimonioChange = (e) => {
    const value = parseInt(e.target.value);
    setPatrimonioSlider(value);
    setPatrimonio(15000000 + (value * 200000));
  };

  // Función para formatear números
  const formatNumber = (num) => {
    return new Intl.NumberFormat('es-ES', {
      style: 'currency',
      currency: 'USD',
      maximumFractionDigits: 0
    }).format(num);
  };

  // Función para restablecer valores
  const restablecerValores = () => {
    setUtilidadNeta(32000000);
    setUtilidadNetaSlider(50);
    setVentas(230342200);
    setVentasSlider(50);
    setActivos(50000000);
    setActivosSlider(50);
    setPatrimonio(23400000);
    setPatrimonioSlider(50);
  };

  // Valores iniciales fijos para comparación
  const valoresIniciales = {
    utilidadNeta: 32000000,
    ventas: 230342200,
    activos: 50000000,
    patrimonio: 23400000,
    roe: 31.8
  };

  // Función para preparar datos de comparación para el gráfico
  const prepararDatosComparacion = () => {
    // Calcular ROE actual
    const margenNeto = utilidadNeta / ventas;
    const rotacionActivos = ventas / activos;
    const multiplicadorCapital = activos / patrimonio;
    const roeActual = margenNeto * rotacionActivos * multiplicadorCapital * 100;

    return [
      {
        nombre: "Utilidad Neta",
        "Valor Original": valoresIniciales.utilidadNeta / 1000000,
        "Valor Simulado": utilidadNeta / 1000000,
        "Diferencia": ((utilidadNeta - valoresIniciales.utilidadNeta) / valoresIniciales.utilidadNeta * 100).toFixed(1)
      },
      {
        nombre: "Ventas",
        "Valor Original": valoresIniciales.ventas / 1000000,
        "Valor Simulado": ventas / 1000000,
        "Diferencia": ((ventas - valoresIniciales.ventas) / valoresIniciales.ventas * 100).toFixed(1)
      },
      {
        nombre: "Activos",
        "Valor Original": valoresIniciales.activos / 1000000,
        "Valor Simulado": activos / 1000000,
        "Diferencia": ((activos - valoresIniciales.activos) / valoresIniciales.activos * 100).toFixed(1)
      },
      {
        nombre: "Patrimonio",
        "Valor Original": valoresIniciales.patrimonio / 1000000,
        "Valor Simulado": patrimonio / 1000000,
        "Diferencia": ((patrimonio - valoresIniciales.patrimonio) / valoresIniciales.patrimonio * 100).toFixed(1)
      },
      {
        nombre: "ROE",
        "Valor Original": valoresIniciales.roe,
        "Valor Simulado": parseFloat(roeActual.toFixed(1)),
        "Diferencia": ((roeActual - valoresIniciales.roe) / valoresIniciales.roe * 100).toFixed(1)
      }
    ];
  };

  // Función para preparar datos para el gráfico lineal
  const prepararDatosGraficoLineal = () => {
    // Calcular ROE actual
    const margenNeto = utilidadNeta / ventas;
    const rotacionActivos = ventas / activos;
    const multiplicadorCapital = activos / patrimonio;
    const roeActual = margenNeto * rotacionActivos * multiplicadorCapital * 100;

    // Crear array con solo dos puntos: valores originales y valores simulados
    return [
      {
        nombre: "Valores Originales",
        "Utilidad Neta": valoresIniciales.utilidadNeta / 1000000,
        "Ventas": valoresIniciales.ventas / 10000000,
        "Activos": valoresIniciales.activos / 1000000,
        "Patrimonio": valoresIniciales.patrimonio / 1000000,
        "ROE": valoresIniciales.roe
      },
      {
        nombre: "Valores Simulados",
        "Utilidad Neta": utilidadNeta / 1000000,
        "Ventas": ventas / 10000000,
        "Activos": activos / 1000000,
        "Patrimonio": patrimonio / 1000000,
        "ROE": parseFloat(roeActual.toFixed(1))
      }
    ];
  };

  return (
    <div className="w-full h-full flex flex-col gap-4">
      <div className="flex w-full flex-col gap-4 justify-start items-start">
        <div
          className="flex gap-1 justify-start items-center cursor-pointer"
          onClick={() => navigate(RoutesPrivate.INICIO)}
        >
          <IconoRegresar
            size={"1.8rem"}
            color={"#909090"}
            salir={true}
            pagina={RoutesPrivate.INICIO}
          />
          <span className=" text-sm font-semibold text-[#909090]">
            Dashboard
          </span>
        </div>
        <div className="flex justify-between w-full">
          <div className="flex flex-col gap-1">
            <span className="text-[#1F263E] text-2xl font-semibold">
              Simulador de Análisis DuPont
            </span>
            <span className="text-[#C3C0C0] text-sm font-base">
              Ajuste los ratios financieros para simular el Retorno sobre el
              Patrimonio (ROE) y comparar con el rendimiento real.
            </span>
            {/* Mostrar información de los parámetros recibidos */}
            {console.log("fechaFinPeriodoInicio", fechaFinPeriodoInicio)}
            {(tipoPeriodoInicio || fechaFinPeriodoInicio || idEstadoFinancieroInicio) && (
              <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <span className="text-blue-800 text-xs font-medium">
                  Datos del periodo seleccionado:
                  {tipoPeriodoInicio && ` Tipo ${tipoPeriodoInicio === 1 ? 'Anual' : 'Mensual'}`}
                  {fechaFinPeriodoInicio && ` | Fecha: ${formatearFechaDDMMYYYY(fechaFinPeriodoInicio)}`}
                  {idEstadoFinancieroInicio && ` | EF ID: ${idEstadoFinancieroInicio}`}
                </span>
              </div>
            )}
          </div>
          <div className="flex gap-4 ">
            <select
              name=""
              id=""
              className="border border-gray-400 rounded-md p-1 min-w-[13rem] outline-none h-[2.5rem]"
            >
              <option value="Greta Labs" selected>
                Greta Labs
              </option>
            </select>
            <select
              name=""
              id=""
              className="border border-gray-400 rounded-md p-1 min-w-[13rem] outline-none h-[2.5rem]"
            >
              {datosMensuales.map((periodo, index) => (
                <option key={index} value={periodo}>
                  {periodo?.nombre} - {periodo?.fecha.split("/")[2]}
                </option>
              ))}
            </select>
            <button
              className="group bg-[#FFFF] text-[#1F263E] p-2 rounded-lg border-[#E4E4E4] border-2 gap-2 flex items-center justify-center hover:bg-[#1F263E] hover:text-[#FFFF] cursor-pointer hover:border-[#1F263E] h-[2.5rem]"
              onClick={() => navigate(RoutesPrivate.COMPARATIVA)}
            >
              <IconoSimulaciones
                size={"1.2rem"}
                className="text-[#1F263E] group-hover:text-[#FFFF]"
              />{" "}
              Comparar Simulaciones
              <IconoFlechaSimular
                size={"1.2rem"}
                className="text-[#1F263E] group-hover:text-[#FFFF]"
              />
            </button>
          </div>
        </div>
      </div>

      <div className="flex w-full gap-4 justify-start items-start mt-3">
        <div className="flex flex-col gap-2 w-[50%] border-1 border-[#EFEFEF] rounded-lg">
          <div className="p-5 flex flex-col gap-2 bg-[#F8FAF8] justify-start items-start">
            <span className="text-[#1F263E] text-xl font-medium">
              Fórmula Dupont
            </span>
            <span className="text-[#C3C0C0] text-sm font-base">
              Ajuste los componentes para ver cómo afectan su Retorno sobre el
              Patrimonio (ROE){" "}
            </span>
          </div>
          <div className="p-5 flex flex-col gap-6 justify-start items-start">
            <div className="flex border-1 border-[#EFEFEF] rounded-lg p-5 w-full">
              {/* Fórmula DuPont visual */}
              <div className="w-full flex flex-col gap-6">
                <div className="flex items-center justify-between w-full">
                  <div className="flex flex-col items-center">
                    <div className="bg-[#F0F0FF] px-4 py-2 rounded-md text-[#5D5FEF] font-medium text-sm">
                      {formatNumber(utilidadNeta)}
                    </div>
                    <span className="text-xs text-[#979797] mt-1">Utilidad Neta</span>
                    <div className="w-full border-t border-gray-300 my-2"></div>
                    <div className="bg-[#FFF0F0] px-4 py-2 rounded-md text-[#FF6B8A] font-medium text-sm">
                      {formatNumber(ventas)}
                    </div>
                    <span className="text-xs text-[#979797] mt-1">Ventas</span>
                  </div>
                  <div className="text-2xl font-bold text-gray-400">×</div>
                  <div className="flex flex-col items-center">
                    <div className="bg-[#FFF0F0] px-4 py-2 rounded-md text-[#FF6B8A] font-medium text-sm">
                      {formatNumber(ventas)}
                    </div>
                    <span className="text-xs text-[#979797] mt-1">Ventas</span>
                    <div className="w-full border-t border-gray-300 my-2"></div>
                    <div className="bg-[#F0FFF0] px-4 py-2 rounded-md text-[#31C969] font-medium text-sm">
                      {formatNumber(activos)}
                    </div>
                    <span className="text-xs text-[#979797] mt-1">Activos</span>
                  </div>
                  <div className="text-2xl font-bold text-gray-400">×</div>
                  <div className="flex flex-col items-center">
                    <div className="bg-[#F0FFF0] px-4 py-2 rounded-md text-[#31C969] font-medium text-sm">
                      {formatNumber(activos)}
                    </div>
                    <span className="text-xs text-[#979797] mt-1">Activos</span>
                    <div className="w-full border-t border-gray-300 my-2"></div>
                    <div className="bg-[#F0F0FF] px-4 py-2 rounded-md text-[#5D5FEF] font-medium text-sm">
                      {formatNumber(patrimonio)}
                    </div>
                    <span className="text-xs text-[#979797] mt-1">Patrimonio</span>
                  </div>
                  <div className="text-2xl font-bold text-gray-400">=</div>
                  <div className="bg-[#FFF8F0] px-4 py-2 rounded-md text-[#FF9F43] font-medium text-sm">
                    {roeSimulado.toFixed(1)}%
                  </div>
                  <span className="text-xs text-[#979797] mt-1">ROE</span>
                </div>
              </div>
            </div>

            {/* Comparación de ROE */}
            <div className="flex border-1 border-[#EFEFEF] rounded-lg p-5 w-full">
              <div className="w-full flex">
                {/* ROE Real */}
                <div className="w-1/2 flex flex-col items-center justify-center border-r border-[#EFEFEF] p-3">
                  <span className="text-[#1F263E] text-sm mb-2">
                    Retorno sobre el Patrimonio (ROE) Real
                  </span>
                  <span className="text-[#FF4D8D] text-3xl font-medium">
                    {roeReal}%
                  </span>
                </div>

                {/* ROE Simulado */}
                <div className="w-1/2 flex flex-col items-center justify-center p-3">
                  <span className="text-[#1F263E] text-sm mb-2">
                    Retorno sobre el Patrimonio (ROE) Simulado
                  </span>
                  <span className="text-[#31C969] text-3xl font-medium">
                    {roeSimulado.toFixed(2)}%
                  </span>
                  <span className="text-[#31C969] text-sm mt-1">
                    Real: {roeReal}% | Diferencia:{" "}
                    {(roeSimulado - roeReal).toFixed(2)}%
                  </span>
                </div>
              </div>
            </div>

            {/* Sliders para ajustar valores */}
            <div className="w-full flex flex-col gap-4 mt-2">
              <div className="flex gap-4">
                {/* Utilidad Neta */}
                <div className="flex flex-col gap-1 w-1/2">
                  <div className="flex justify-between">
                    <span className="text-[#1F263E] text-sm font-medium">
                      Utilidad neta
                    </span>
                    <div className="border border-[#EFEFEF] rounded px-2 py-1 text-sm">
                      {formatNumber(utilidadNeta)}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={utilidadNetaSlider}
                      onChange={handleUtilidadNetaChange}
                      className="w-full h-2 bg-blue-200 rounded-lg appearance-none cursor-pointer"
                      style={{
                        background: `linear-gradient(to right, #5D5FEF ${utilidadNetaSlider}%, #E5E7EB ${utilidadNetaSlider}%)`,
                      }}
                    />
                  </div>
                </div>

                {/* Activos */}
                <div className="flex flex-col gap-1 w-1/2">
                  <div className="flex justify-between">
                    <span className="text-[#1F263E] text-sm font-medium">
                      Activos
                    </span>
                    <div className="border border-[#EFEFEF] rounded px-2 py-1 text-sm">
                      {formatNumber(activos)}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={activosSlider}
                      onChange={handleActivosChange}
                      className="w-full h-2 bg-purple-200 rounded-lg appearance-none cursor-pointer"
                      style={{
                        background: `linear-gradient(to right, #737507 ${activosSlider}%, #E5E7EB ${activosSlider}%)`,
                      }}
                    />
                  </div>
                </div>
              </div>

              <div className="flex gap-4">
                {/* Ventas */}
                <div className="flex flex-col gap-1 w-1/2">
                  <div className="flex justify-between">
                    <span className="text-[#1F263E] text-sm font-medium">
                      Ventas
                    </span>
                    <div className="border border-[#EFEFEF] rounded px-2 py-1 text-sm">
                      {formatNumber(ventas)}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={ventasSlider}
                      onChange={handleVentasChange}
                      className="w-full h-2 bg-green-200 rounded-lg appearance-none cursor-pointer"
                      style={{
                        background: `linear-gradient(to right, #34A853 ${ventasSlider}%, #E5E7EB ${ventasSlider}%)`,
                      }}
                    />
                  </div>
                </div>

                {/* Patrimonio */}
                <div className="flex flex-col gap-1 w-1/2">
                  <div className="flex justify-between">
                    <span className="text-[#1F263E] text-sm font-medium">
                      Patrimonio
                    </span>
                    <div className="border border-[#EFEFEF] rounded px-2 py-1 text-sm">
                      {formatNumber(patrimonio)}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={patrimonioSlider}
                      onChange={handlePatrimonioChange}
                      className="w-full h-2 bg-orange-200 rounded-lg appearance-none cursor-pointer"
                      style={{
                        background: `linear-gradient(to right, #FF6B00 ${patrimonioSlider}%, #E5E7EB ${patrimonioSlider}%)`,
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="flex flex-col gap-2 w-[50%] border-1 border-[#EFEFEF] rounded-lg">
          <div className="p-5 flex flex-col gap-2 bg-[#F8FAF8] justify-start items-start">
            <span className="text-[#1F263E] text-xl font-medium">Ratios</span>
            <span className="text-[#C3C0C0] text-sm font-base">
              Impacto en el rendimiento financiero
            </span>
          </div>
          <div className="p-5 flex gap-4 w-full">
            <div className="flex flex-col w-[50%] gap-6">
              {/* Ratios financieros - columna izquierda */}
              <div className="flex flex-col w-full gap-2">
                <div className="flex justify-between w-full">
                  <div className="flex justify-start items-center gap-2">
                    <span className="text-[#1F263E] text-md font-base">
                      Ratio de Liquidez
                    </span>
                    <IconoInformacion size={"1rem"} color={"#979797"} />
                  </div>
                  <span className="text-[#1F263E] text-md font-base">5.2%</span>
                </div>

              </div>

              <div className="flex flex-col w-full gap-1">
                <div className="flex justify-between w-full">
                  <div className="flex justify-start items-center gap-2">
                    <span className="text-[#1F263E] text-md font-base">
                      Capital del Trabajo
                    </span>
                    <IconoInformacion size={"1rem"} color={"#979797"} />
                  </div>
                  <span className="text-[#1F263E] text-md font-base">5.2%</span>
                </div>

              </div>
              <div className="flex flex-col w-full gap-1">
                <div className="flex justify-between w-full">
                  <div className="flex justify-start items-center gap-2">
                    <span className="text-[#1F263E] text-md font-base">
                      Ratio de Endeudamiento
                    </span>
                    <IconoInformacion size={"1rem"} color={"#979797"} />
                  </div>
                  <span className="text-[#1F263E] text-md font-base">5.2%</span>
                </div>

              </div>
              <div className="flex flex-col w-full gap-1">
                <div className="flex justify-between w-full">
                  <div className="flex justify-start items-center gap-2">
                    <span className="text-[#1F263E] text-md font-base">
                      Endeudamiento a corto plazo
                    </span>
                    <IconoInformacion size={"1rem"} color={"#979797"} />
                  </div>
                  <span className="text-[#1F263E] text-md font-base">5.2%</span>
                </div>

              </div>
              <div className="flex flex-col w-full gap-1">
                <div className="flex justify-between w-full">
                  <div className="flex justify-start items-center gap-2">
                    <span className="text-[#1F263E] text-md font-base">
                      Endeudamiento a largo plazo
                    </span>
                    <IconoInformacion size={"1rem"} color={"#979797"} />
                  </div>
                  <span className="text-[#1F263E] text-md font-base">5.2%</span>
                </div>

              </div>
              <div className="flex flex-col w-full gap-1">
                <div className="flex justify-between w-full">
                  <div className="flex justify-start items-center gap-2">
                    <span className="text-[#1F263E] text-md font-base">
                      Rentabilidad del Activo (ROA)
                    </span>
                    <IconoInformacion size={"1rem"} color={"#979797"} />
                  </div>
                  <span className="text-[#1F263E] text-md font-base">5.2%</span>
                </div>

              </div>
            </div>
            <div className="flex flex-col w-[50%] gap-6">
              {/* Ratios financieros - columna derecha */}
              <div className="flex flex-col w-full gap-1">
                <div className="flex justify-between w-full">
                  <div className="flex justify-start items-center gap-2">
                    <span className="text-[#1F263E] text-md font-base">
                      Prueba Ácida
                    </span>
                    <IconoInformacion size={"1rem"} color={"#979797"} />
                  </div>
                  <span className="text-[#1F263E] text-md font-base">5.2%</span>
                </div>

              </div>
              <div className="flex flex-col w-full gap-1">
                <div className="flex justify-between w-full">
                  <div className="flex justify-start items-center gap-2">
                    <span className="text-[#1F263E] text-md font-base">
                      Rentabilidad Financiera (ROE)
                    </span>
                    <IconoInformacion size={"1rem"} color={"#979797"} />
                  </div>
                  <span className="text-[#1F263E] text-md font-base">5.2%</span>
                </div>

              </div>
              <div className="flex flex-col w-full gap-1">
                <div className="flex justify-between w-full">
                  <div className="flex justify-start items-center gap-2">
                    <span className="text-[#1F263E] text-md font-base">
                      Rentabilidad de las ventas
                    </span>
                    <IconoInformacion size={"1rem"} color={"#979797"} />
                  </div>
                  <span className="text-[#1F263E] text-md font-base">5.2%</span>
                </div>

              </div>
            </div>
          </div>

          {/* Componentes DuPont separados */}
          <div className="p-5 pt-0">
            <div className="flex justify-between mt-8">
              {/* Margen Neto */}
              <div className="flex flex-col items-center">
                <span className="text-[#1F263E] text-md font-medium mb-2">Margen Neto</span>
                <div className="flex items-center gap-4">
                  <div className="flex flex-col items-center">
                    <div className="bg-[#F0F0FF] px-3 py-1 rounded text-[#5D5FEF] font-medium text-md mb-1">
                      <span className="text-xs text-[#979797]">Utilidad neta</span>
                      <div className="font-semibold">{formatNumber(utilidadNeta)}</div>
                    </div>
                    <div className="w-full border-t border-gray-300 my-1"></div>
                    <div className="bg-[#E6F7EA] px-3 py-1 rounded text-[#34A853] font-medium text-md mt-1">
                      <span className="text-xs text-[#979797]">Ventas</span>
                      <div className="font-semibold">{formatNumber(ventas)}</div>
                    </div>
                  </div>
                  <div className="bg-[#FFF8E0] px-4 py-2 rounded-md text-[#FF9F43] font-medium text-md flex items-center justify-center min-w-[80px]">
                    {(margenNeto * 100).toFixed(2)}%
                  </div>
                </div>
              </div>

              {/* Rotación de Activos */}
              <div className="flex flex-col items-center">
                <span className="text-[#1F263E] text-md font-medium mb-2">Rotación de Activos</span>
                <div className="flex items-center gap-4">
                  <div className="flex flex-col items-center">
                    <div className="bg-[#E6F7EA] px-3 py-1 rounded text-[#34A853] font-medium text-md mb-1">
                      <span className="text-xs text-[#979797]">Ventas</span>
                      <div className="font-semibold">{formatNumber(ventas)}</div>
                    </div>
                    <div className="w-full border-t border-gray-300 my-1"></div>
                    <div className="bg-[#EEE6FF] px-3 py-1 rounded text-[#5D5FEF] font-medium text-md mt-1">
                      <span className="text-xs text-[#979797]">Activos</span>
                      <div className="font-semibold">{formatNumber(activos)}</div>
                    </div>
                  </div>
                  <div className="bg-[#FFF8E0] px-4 py-2 rounded-md text-[#FF9F43] font-medium text-md flex items-center justify-center min-w-[80px]">
                    {rotacionActivos.toFixed(2)}
                  </div>
                </div>
              </div>

              {/* Apalancamiento */}
              <div className="flex flex-col items-center">
                <span className="text-[#1F263E] text-md font-medium mb-2">Apalancamiento</span>
                <div className="flex items-center gap-4">
                  <div className="flex flex-col items-center">
                    <div className="bg-[#EEE6FF] px-3 py-1 rounded text-[#5D5FEF] font-medium text-md mb-1">
                      <span className="text-xs text-[#979797]">Activos</span>
                      <div className="font-semibold">{formatNumber(activos)}</div>
                    </div>
                    <div className="w-full border-t border-gray-300 my-1"></div>
                    <div className="bg-[#FFF8E0] px-3 py-1 rounded text-[#FF9F43] font-medium text-md mt-1">
                      <span className="text-xs text-[#979797]">Patrimonio</span>
                      <div className="font-semibold">{formatNumber(patrimonio)}</div>
                    </div>
                  </div>
                  <div className="bg-[#FFF8E0] px-4 py-2 rounded-md text-[#FF9F43] font-medium text-md flex items-center justify-center min-w-[80px]">
                    {multiplicadorCapital.toFixed(2)}
                  </div>
                </div>
              </div>
            </div>
          </div>


        </div>
      </div>

          <div className="mt-2 p-4 bg-[#F8FAF8] rounded-lg">
            <h3 className="text-[#1F263E] font-medium mb-2">Resumen de cambios</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              {prepararDatosComparacion().map((item) => (
                <div key={item.nombre} className="flex flex-col p-3 bg-white rounded-lg shadow-sm">
                  <span className="text-sm text-[#6B7280]">{item.nombre}</span>
                  <div className="flex justify-between items-center mt-1">
                    <span className="text-lg font-medium text-[#1F263E]">
                      {item.nombre === "ROE"
                        ? `${item["Valor Simulado"]}%`
                        : `${item["Valor Simulado"]}M`}
                    </span>
                    <span className={`text-sm font-medium ${parseFloat(item["Diferencia"]) >= 0 ? 'text-[#34A853]' : 'text-[#FF4D4D]'}`}>
                      {parseFloat(item["Diferencia"]) >= 0 ? '+' : ''}{item["Diferencia"]}%
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

      {/* Botones de acción */}
      <div className="flex w-full justify-end items-center gap-4 p-7.5">
        <button
          onClick={restablecerValores}
          className="group bg-[#FFFF] text-[#1F263E] p-2 rounded-lg border-[#E4E4E4] border-2 gap-2 flex items-center justify-center hover:bg-[#1F263E] hover:text-[#FFFF] cursor-pointer hover:border-[#1F263E] h-[2.5rem]">
          <IconoReestablecer size={"1.5rem"} className="text-[#1F263E] group-hover:text-[#FFFF]" /> Restablecer Valores
        </button>
        <button
          className="group bg-[#1F263E] text-[#FFFF] p-2 rounded-lg border-[#1F263E] border-2 gap-2 flex items-center justify-center hover:bg-[#1F263E] hover:text-[#FFFF] cursor-pointer hover:border-[#1F263E] h-[2.5rem]">
          Guardar Simulación
        </button>
      </div>
     </div>
  );
};

export default Simulaciones;
